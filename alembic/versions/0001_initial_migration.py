"""Initial migration for scheduler service

Revision ID: 0001
Revises: 
Create Date: 2025-01-17 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Upgrade database schema."""
    # Create schedulers table
    op.create_table('schedulers',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', sa.String(length=255), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('schedule_type', sa.String(length=50), nullable=False),
        sa.Column('schedule_config', sa.JSON(), nullable=False),
        sa.Column('workflow_config', sa.JSON(), nullable=False),
        sa.Column('timezone', sa.String(length=255), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('max_executions', sa.Integer(), nullable=True),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('last_run_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('next_run_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('workflow_id', sa.String(length=255), nullable=False),
        sa.Column('scheduler_metadata', sa.JSON(), nullable=True),
        sa.Column('frequency', sa.String(length=50), nullable=False),
        sa.Column('time', sa.String(length=5), nullable=True),
        sa.Column('days_of_week', sa.JSON(), nullable=True),
        sa.Column('days_of_month', sa.JSON(), nullable=True),
        sa.Column('cron_expression', sa.String(length=255), nullable=True),
        sa.Column('input_values', sa.JSON(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create scheduler_executions table
    op.create_table('scheduler_executions',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('scheduler_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('execution_time', sa.DateTime(timezone=True), nullable=False),
        sa.Column('status', sa.String(length=50), nullable=False),
        sa.Column('result', sa.JSON(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('duration_ms', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['scheduler_id'], ['schedulers.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for better performance
    op.create_index('idx_schedulers_user_id', 'schedulers', ['user_id'])
    op.create_index('idx_schedulers_schedule_type', 'schedulers', ['schedule_type'])
    op.create_index('idx_schedulers_is_active', 'schedulers', ['is_active'])
    op.create_index('idx_schedulers_expires_at', 'schedulers', ['expires_at'])
    op.create_index('idx_schedulers_frequency', 'schedulers', ['frequency'])
    op.create_index('idx_schedulers_time', 'schedulers', ['time'])
    op.create_index('idx_schedulers_frequency_active', 'schedulers', ['frequency', 'is_active'])
    op.create_index('idx_schedulers_next_run_active', 'schedulers', ['next_run_at', 'is_active'])
    op.create_index('idx_schedulers_due_processing', 'schedulers', ['is_active', 'next_run_at', 'expires_at'])
    
    op.create_index('idx_scheduler_executions_scheduler_id', 'scheduler_executions', ['scheduler_id'])
    op.create_index('idx_scheduler_executions_status', 'scheduler_executions', ['status'])
    op.create_index('idx_scheduler_executions_execution_time', 'scheduler_executions', ['execution_time'])


def downgrade() -> None:
    """Downgrade database schema."""
    # Drop indexes
    op.drop_index('idx_scheduler_executions_execution_time', table_name='scheduler_executions')
    op.drop_index('idx_scheduler_executions_status', table_name='scheduler_executions')
    op.drop_index('idx_scheduler_executions_scheduler_id', table_name='scheduler_executions')
    
    op.drop_index('idx_schedulers_due_processing', table_name='schedulers')
    op.drop_index('idx_schedulers_next_run_active', table_name='schedulers')
    op.drop_index('idx_schedulers_frequency_active', table_name='schedulers')
    op.drop_index('idx_schedulers_time', table_name='schedulers')
    op.drop_index('idx_schedulers_frequency', table_name='schedulers')
    op.drop_index('idx_schedulers_expires_at', table_name='schedulers')
    op.drop_index('idx_schedulers_is_active', table_name='schedulers')
    op.drop_index('idx_schedulers_schedule_type', table_name='schedulers')
    op.drop_index('idx_schedulers_user_id', table_name='schedulers')
    
    # Drop tables
    op.drop_table('scheduler_executions')
    op.drop_table('schedulers')
