-- Database initialization script for Scheduler Service
-- This script sets up the initial database structure and permissions

-- Create extensions if they don't exist
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create indexes for better performance (these will be created by Alembic migrations)
-- This is just a placeholder for any additional setup needed

-- Grant necessary permissions
GRANT ALL PRIVILEGES ON DATABASE scheduler_db TO scheduler_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO scheduler_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO scheduler_user;

-- Set default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO scheduler_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO scheduler_user;
