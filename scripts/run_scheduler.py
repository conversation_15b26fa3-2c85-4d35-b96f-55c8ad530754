#!/usr/bin/env python3
"""
Scheduler Service Runner Script.

This script provides a simple way to run the scheduler service engine
for processing scheduled workflows.
"""

import asyncio
import signal
import sys
from pathlib import Path

# Add the src directory to the Python path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from src.core.scheduler_service import SchedulerService
from src.database.connection import init_database, close_database
from src.utils.config import get_settings
from src.utils.logger import setup_logging, get_logger


async def main():
    """
    Main function to run the scheduler service.
    """
    settings = get_settings()

    # Setup logging
    setup_logging(log_level=settings.log_level, log_format=settings.log_format)

    logger = get_logger(__name__)
    logger.info("Starting Scheduler Service...")

    # Initialize database
    try:
        await init_database()
        logger.info("Database initialized")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}", exc_info=True)
        return 1

    # Create scheduler service
    scheduler_service = None
    try:
        scheduler_service = SchedulerService(
            redis_url=settings.redis_url,
            max_concurrent_schedulers=settings.max_concurrent_schedulers,
            batch_size=settings.scheduler_batch_size,
            worker_concurrency=settings.task_worker_concurrency,
        )

        # Initialize scheduler service
        await scheduler_service.initialize()
        logger.info("Scheduler service initialized")

        # Setup signal handlers for graceful shutdown
        shutdown_event = asyncio.Event()

        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating shutdown...")
            shutdown_event.set()

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        # Start scheduler service
        await scheduler_service.start()
        logger.info("Scheduler service started successfully")

        # Wait for shutdown signal
        logger.info("Scheduler service is running. Press Ctrl+C to stop.")
        await shutdown_event.wait()

        return 0

    except Exception as e:
        logger.error(f"Error running scheduler service: {e}", exc_info=True)
        return 1

    finally:
        # Cleanup
        if scheduler_service:
            try:
                logger.info("Shutting down scheduler service...")
                await scheduler_service.stop()
                logger.info("Scheduler service stopped")
            except Exception as e:
                logger.error(f"Error stopping scheduler service: {e}", exc_info=True)

        try:
            await close_database()
            logger.info("Database connections closed")
        except Exception as e:
            logger.error(f"Error closing database: {e}", exc_info=True)

        logger.info("Scheduler service shutdown complete")


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
        sys.exit(0)
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)
