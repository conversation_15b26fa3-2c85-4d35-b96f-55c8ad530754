#!/usr/bin/env python3
"""
Test runner script for the Scheduler Service.

This script provides a simple way to run tests with proper environment setup.
"""

import os
import sys
import subprocess
from pathlib import Path

# Add the src directory to the Python path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))


def run_tests(test_type="all", verbose=False, coverage=False):
    """
    Run tests for the scheduler service.
    
    Args:
        test_type: Type of tests to run ("unit", "integration", "e2e", "all")
        verbose: Whether to run tests in verbose mode
        coverage: Whether to generate coverage reports
    """
    # Set environment variables for testing
    os.environ["DATABASE_URL"] = "sqlite+aiosqlite:///:memory:"
    os.environ["REDIS_URL"] = "redis://localhost:6379/15"
    os.environ["LOG_LEVEL"] = "DEBUG"
    os.environ["DEBUG"] = "true"
    
    # Build pytest command
    cmd = ["python", "-m", "pytest"]
    
    # Add test directory based on type
    if test_type == "unit":
        cmd.append("tests/unit/")
    elif test_type == "integration":
        cmd.append("tests/integration/")
    elif test_type == "e2e":
        cmd.append("tests/e2e/")
    else:
        cmd.append("tests/")
    
    # Add options
    if verbose:
        cmd.append("-v")
    
    if coverage:
        cmd.extend([
            "--cov=scheduler_service",
            "--cov-report=html",
            "--cov-report=term-missing",
            "--cov-report=xml",
        ])
    
    # Add markers
    cmd.extend([
        "-m", f"{test_type}" if test_type != "all" else "not slow",
        "--tb=short",
    ])
    
    print(f"Running {test_type} tests...")
    print(f"Command: {' '.join(cmd)}")
    
    # Run tests
    try:
        result = subprocess.run(cmd, check=False)
        return result.returncode
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        return 1
    except Exception as e:
        print(f"Error running tests: {e}")
        return 1


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Run Scheduler Service tests")
    parser.add_argument(
        "--type",
        choices=["unit", "integration", "e2e", "all"],
        default="all",
        help="Type of tests to run",
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Run tests in verbose mode",
    )
    parser.add_argument(
        "--coverage", "-c",
        action="store_true",
        help="Generate coverage reports",
    )
    parser.add_argument(
        "--quick", "-q",
        action="store_true",
        help="Run only unit tests (quick mode)",
    )
    
    args = parser.parse_args()
    
    # Quick mode overrides type
    if args.quick:
        test_type = "unit"
    else:
        test_type = args.type
    
    # Run tests
    exit_code = run_tests(
        test_type=test_type,
        verbose=args.verbose,
        coverage=args.coverage,
    )
    
    if exit_code == 0:
        print(f"\n✅ {test_type.title()} tests passed!")
    else:
        print(f"\n❌ {test_type.title()} tests failed!")
    
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
