# Scheduler Service Optimization Documentation
## Concurrent Call Processing Implementation

### Overview

This document outlines the optimization strategy for the Scheduler Service to enable processing a minimum of 50 concurrent calls. The current implementation processes batch calls and contacts sequentially, which limits throughput and efficiency. This optimization introduces concurrency at multiple levels to significantly improve call processing capacity.

### Table of Contents

1. [Current Implementation Analysis](#current-implementation-analysis)
2. [Optimization Goals](#optimization-goals)
3. [Technical Approach](#technical-approach)
4. [Implementation Details](#implementation-details)
5. [Performance Considerations](#performance-considerations)
6. [Monitoring and Observability](#monitoring-and-observability)
7. [Configuration Options](#configuration-options)
8. [Testing Strategy](#testing-strategy)
9. [Deployment Considerations](#deployment-considerations)
10. [Rollback Plan](#rollback-plan)

## Current Implementation Analysis

The current implementation has several limitations that prevent efficient concurrent processing:

### Sequential Processing Bottlenecks

1. **Batch Call Fetching**:
   - Only one batch call is fetched at a time (`page_size=1`)
   - Each batch call is processed completely before moving to the next

2. **Contact Processing**:
   - Contacts within a batch are processed one by one in a sequential loop
   - No parallelization of outbound call initiation

3. **External Service Interactions**:
   - No connection pooling or concurrent request handling for external services
   - Each call to LiveKit and Call Service is made sequentially

### Code Analysis

Key bottlenecks in the current implementation:

```python
# BatchService.process_batch_calls
async def process_batch_calls(self):
    try:
        batch_calls = await self.call_service.get_all_batch_calls(page=1, page_size=1)
        
        if not batch_calls:
            logger.info("No pending batch calls found to process.")
            return

        for batch_call in batch_calls:
            logger.info(f"Processing batch call ID: {batch_call.id}, Title: {batch_call.title}")
            await self.process_single_batch_call(batch_call)
    except Exception as e:
        logger.error(f"Error processing batch calls: {e}", exc_info=True)

# BatchService.process_single_batch_call
async def process_single_batch_call(self, batch_call: call_pb2.BatchCall):
    # ...
    for index, contact_item in enumerate(all_contacts):
        if not contact_item.is_valid:
            logger.warning(f"[{batch_call_id}] Skipping invalid contact with phone: {contact_item.phone_number}")
            continue

        logger.info(f"[{batch_call_id}] Processing contact {index+1}/{len(all_contacts)}")
        try:
            # Sequential processing of each contact
            # ...
        except Exception as call_exc:
            logger.error(f"[{batch_call_id}] Error initiating call for contact {index+1}: {call_exc}", exc_info=True)
            all_calls_initiated = False
```

## Optimization Goals

1. **Primary Goal**: Process a minimum of 50 calls concurrently
2. **Secondary Goals**:
   - Maintain system stability under increased load
   - Ensure proper error handling and fault tolerance
   - Implement monitoring for concurrent operations
   - Allow for configuration of concurrency limits
   - Optimize resource usage (CPU, memory, network)

## Technical Approach

The optimization strategy focuses on introducing concurrency at multiple levels:

### 1. Contact-Level Concurrency

Implement asyncio task-based concurrency to process multiple contacts simultaneously within a single batch call. This is the primary mechanism for achieving 50+ concurrent calls.

### 2. Batch-Level Concurrency

Process multiple batch calls concurrently to further increase throughput when multiple batch calls are pending.

### 3. Rate Limiting and Throttling

Implement semaphores to control the maximum number of concurrent operations and prevent overwhelming external services.

### 4. Monitoring and Metrics

Add instrumentation to track concurrent operations, resource usage, and performance metrics.

## Implementation Details

### 1. Contact-Level Concurrency

#### Modified BatchService Class

```python
class BatchService:
    def __init__(self, call_service: CallService, livekit_service: LiveKitService, bucket_name: str):
        self.call_service = call_service
        self.livekit_service = livekit_service
        self.bucket_name = bucket_name
        
        # Concurrency control
        self.max_concurrent_calls = 50  # Configurable
        self.call_semaphore = asyncio.Semaphore(self.max_concurrent_calls)
        
        # Monitoring
        self.active_calls = 0
        self.active_calls_lock = asyncio.Lock()
        
    async def _increment_active_calls(self):
        async with self.active_calls_lock:
            self.active_calls += 1
            logger.info(f"Active calls: {self.active_calls}")

    async def _decrement_active_calls(self):
        async with self.active_calls_lock:
            self.active_calls -= 1
            logger.info(f"Active calls: {self.active_calls}")
```

#### Concurrent Contact Processing

```python
async def process_single_batch_call(self, batch_call: call_pb2.BatchCall):
    batch_call_id = batch_call.id
    
    try:
        # Fetch contact details
        contact = await self.call_service.get_contact_by_id(batch_call.contact_id)
        
        if not contact:
            logger.error(f"[{batch_call_id}] Contact details not found for contact_id {batch_call.contact_id}.")
            await self.call_service.update_batch_status(batch_call_id, "failed")
            return

        logger.info(f"[{batch_call_id}] Contact found. Agent ID: {contact.agent_id}. Fetching contact lists...")

        # Get all contacts with pagination (existing code)
        all_contacts = []
        current_page = 1
        page_size = 100
        
        while True:
            contact_lists_response = await self.call_service.get_contact_lists(
                batch_call.contact_id, current_page, page_size
            )
            
            if not contact_lists_response.data:
                break
                
            all_contacts.extend(contact_lists_response.data)
            
            if current_page >= contact_lists_response.metadata.totalPages:
                break
                
            current_page += 1

        if not all_contacts:
            logger.warning(f"[{batch_call_id}] No contacts found. Marking batch as completed.")
            await self.call_service.update_batch_status(batch_call_id, "ended")
            return

        logger.info(f"[{batch_call_id}] Found {len(all_contacts)} contacts. Processing concurrently.")

        # Process contacts concurrently
        tasks = []
        for index, contact_item in enumerate(all_contacts):
            if not contact_item.is_valid:
                logger.warning(f"[{batch_call_id}] Skipping invalid contact with phone: {contact_item.phone_number}")
                continue
                
            # Create a task for each contact
            task = asyncio.create_task(
                self._process_contact(batch_call, contact, contact_item, index)
            )
            tasks.append(task)
        
        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        success_count = 0
        failure_count = 0
        
        for result in results:
            if isinstance(result, Exception):
                failure_count += 1
                logger.error(f"[{batch_call_id}] Task exception: {result}")
            elif result is True:
                success_count += 1
            else:
                failure_count += 1
                
        logger.info(f"[{batch_call_id}] Batch processing complete. Success: {success_count}, Failures: {failure_count}")
        
        # Update batch status based on results
        if failure_count == 0 and success_count > 0:
            await self.call_service.update_batch_status(batch_call_id, "complete")
        elif success_count == 0:
            await self.call_service.update_batch_status(batch_call_id, "failed")
        else:
            await self.call_service.update_batch_status(batch_call_id, "partial")

    except Exception as e:
        logger.error(f"[{batch_call_id}] Error processing batch call: {e}", exc_info=True)
        await self.call_service.update_batch_status(batch_call_id, "failed")
```

#### Contact Processing Helper Method

```python
async def _process_contact(self, batch_call, contact, contact_item, index):
    batch_call_id = batch_call.id
    
    # Use semaphore to limit concurrent calls
    async with self.call_semaphore:
        await self._increment_active_calls()
        try:
            logger.info(f"[{batch_call_id}] Processing contact {index+1}")
            
            # Create contact data dictionary
            contact_data = {'phone_number': contact_item.phone_number}
            
            try:
                metadata = json.loads(contact_item.csv_metadata)
                contact_data.update(metadata)
            except json.JSONDecodeError as e:
                logger.warning(f"[{batch_call_id}] Failed to parse csv_metadata for contact {index+1}: {e}")
                contact_data['Name'] = 'Customer'  # Default value
            
            contact_row = pd.Series(contact_data)
            
            # Initiate the call
            result = await self.initiate_outbound_call(batch_call, contact, contact_row)
            return result
            
        except Exception as e:
            logger.error(f"[{batch_call_id}] Error processing contact {index+1}: {e}", exc_info=True)
            return False
        finally:
            await self._decrement_active_calls()
```

### 2. Batch-Level Concurrency

```python
async def process_batch_calls(self):
    """Process pending batch calls from the queue with concurrency"""
    try:
        # Fetch multiple batch calls at once
        batch_calls = await self.call_service.get_all_batch_calls(page=1, page_size=10)
        
        if not batch_calls:
            logger.info("No pending batch calls found to process.")
            return

        logger.info(f"Found {len(batch_calls)} batch calls to process.")
        
        # Process batch calls concurrently
        batch_tasks = []
        for batch_call in batch_calls:
            logger.info(f"Creating task for batch call ID: {batch_call.id}, Title: {batch_call.title}")
            task = asyncio.create_task(self.process_single_batch_call(batch_call))
            batch_tasks.append(task)
            
        # Wait for all batch processing to complete
        await asyncio.gather(*batch_tasks, return_exceptions=True)
        
    except Exception as e:
        logger.error(f"Error processing batch calls: {e}", exc_info=True)
```

### 3. Optimized External Service Interactions

#### Connection Pooling for gRPC

```python
class CallService:
    def __init__(self, host: str, port: int):
        self.host = host
        self.port = port
        
        # Create channel options for concurrent requests
        self.options = [
            ('grpc.max_concurrent_streams', 100),
            ('grpc.max_receive_message_length', 10 * 1024 * 1024),  # 10 MB
            ('grpc.max_send_message_length', 10 * 1024 * 1024),     # 10 MB
        ]
        
        self.channel = grpc.insecure_channel(f"{host}:{port}", options=self.options)
        self.stub = call_pb2_grpc.CallServiceStub(self.channel)
```

## Performance Considerations

### 1. Resource Management

- **Memory Usage**: Concurrent operations will increase memory usage. Monitor and adjust based on available resources.
- **CPU Utilization**: Asyncio is single-threaded but I/O bound operations benefit from concurrency. Monitor CPU usage.
- **Network Connections**: Ensure connection pools are properly sized for concurrent operations.

### 2. External Service Capacity

- **LiveKit Capacity**: Verify LiveKit can handle 50+ concurrent connections.
- **gRPC Service Capacity**: Ensure the Call Service can handle increased request volume.
- **Database Connection Pooling**: If applicable, ensure database connections are properly pooled.

### 3. Error Handling and Resilience

- **Circuit Breaking**: Implement circuit breaking to prevent cascading failures.
- **Retry Logic**: Add exponential backoff for transient failures.
- **Graceful Degradation**: Reduce concurrency if external services show signs of stress.

```python
# Example retry logic with exponential backoff
async def retry_with_backoff(func, *args, max_retries=3, **kwargs):
    retries = 0
    while retries < max_retries:
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            retries += 1
            if retries >= max_retries:
                logger.error(f"Max retries reached for {func.__name__}: {e}")
                raise
                
            wait_time = 2 ** retries  # Exponential backoff
            logger.warning(f"Retry {retries}/{max_retries} for {func.__name__} after {wait_time}s: {e}")
            await asyncio.sleep(wait_time)
```

## Monitoring and Observability

### 1. Metrics to Track

- **Active Concurrent Calls**: Number of calls being processed simultaneously
- **Call Processing Rate**: Calls processed per minute
- **Success/Failure Rates**: Percentage of successful vs. failed calls
- **Processing Time**: Time taken to process each call
- **Resource Utilization**: CPU, memory, and network usage

### 2. Logging Enhancements

```python
# Enhanced logging with context
async def _process_contact(self, batch_call, contact, contact_item, index):
    batch_call_id = batch_call.id
    phone_number = contact_item.phone_number
    
    log_context = {
        'batch_id': batch_call_id,
        'contact_index': index,
        'phone': phone_number,
        'concurrent_calls': self.active_calls
    }
    
    async with self.call_semaphore:
        await self._increment_active_calls()
        start_time = time.time()
        
        try:
            logger.info(f"Processing contact", extra=log_context)
            # Processing logic...
            
            processing_time = time.time() - start_time
            logger.info(f"Contact processed successfully in {processing_time:.2f}s", extra=log_context)
            return True
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Error processing contact after {processing_time:.2f}s: {e}", 
                        exc_info=True, extra=log_context)
            return False
        finally:
            await self._decrement_active_calls()
```

### 3. Health Checks

```python
async def get_health_status(self):
    """Return health status of the batch service"""
    return {
        'active_calls': self.active_calls,
        'max_concurrent_calls': self.max_concurrent_calls,
        'call_service_connected': self._check_call_service_connection(),
        'livekit_service_connected': self._check_livekit_connection()
    }
```

## Configuration Options

### 1. Environment Variables

Add the following environment variables to make concurrency configurable:

```
# Concurrency Settings
MAX_CONCURRENT_CALLS=50
MAX_BATCH_SIZE=10
CALL_TIMEOUT_SECONDS=60
RETRY_ATTEMPTS=3
```

### 2. Dynamic Configuration

```python
class BatchService:
    def __init__(self, call_service: CallService, livekit_service: LiveKitService, bucket_name: str):
        self.call_service = call_service
        self.livekit_service = livekit_service
        self.bucket_name = bucket_name
        
        # Load configuration from environment
        self.max_concurrent_calls = int(os.getenv('MAX_CONCURRENT_CALLS', '50'))
        self.max_batch_size = int(os.getenv('MAX_BATCH_SIZE', '10'))
        self.call_timeout = int(os.getenv('CALL_TIMEOUT_SECONDS', '60'))
        self.retry_attempts = int(os.getenv('RETRY_ATTEMPTS', '3'))
        
        # Initialize concurrency controls
        self.call_semaphore = asyncio.Semaphore(self.max_concurrent_calls)
        self.active_calls = 0
        self.active_calls_lock = asyncio.Lock()
```

## Testing Strategy

### 1. Unit Tests

```python
# Example unit test for concurrent processing
async def test_concurrent_contact_processing():
    # Setup mock services
    mock_call_service = MockCallService()
    mock_livekit_service = MockLiveKitService()
    
    # Create batch service with test configuration
    batch_service = BatchService(
        call_service=mock_call_service,
        livekit_service=mock_livekit_service,
        bucket_name="test-bucket"
    )
    batch_service.max_concurrent_calls = 10  # Lower for testing
    
    # Create test batch call with multiple contacts
    batch_call = create_test_batch_call(contact_count=20)
    
    # Process the batch call
    await batch_service.process_single_batch_call(batch_call)
    
    # Verify all contacts were processed
    assert mock_call_service.processed_count == 20
    
    # Verify concurrency was used (max active calls should be 10)
    assert mock_call_service.max_concurrent_calls <= 10
```

### 2. Load Testing

- Test with varying numbers of contacts (10, 50, 100, 500)
- Monitor resource usage during load tests
- Verify system stability under maximum load
- Test recovery from failures during concurrent processing

### 3. Integration Testing

- Test end-to-end flow with real or simulated external services
- Verify correct handling of various error conditions
- Test with different concurrency configurations

## Deployment Considerations

### 1. Resource Requirements

- **CPU**: Minimum 2 cores recommended
- **Memory**: Minimum 2GB, recommended 4GB for 50+ concurrent calls
- **Network**: Ensure sufficient bandwidth for concurrent connections

### 2. Scaling Strategy

- **Vertical Scaling**: Increase resources (CPU/memory) for higher concurrency
- **Horizontal Scaling**: Deploy multiple instances for very high throughput

### 3. Monitoring Setup

- Configure alerts for:
  - High failure rates
  - Resource utilization thresholds
  - External service errors
  - Processing time anomalies

## Rollback Plan

### 1. Immediate Rollback Procedure

If issues are detected after deployment:

1. Revert to previous code version
2. Reduce concurrency limits via environment variables
3. Implement circuit breaking to protect external services

### 2. Gradual Rollout Strategy

1. Deploy with lower concurrency limits initially (e.g., 10)
2. Monitor system performance and stability
3. Gradually increase limits to target (50+)
4. Continue monitoring at each step

## Conclusion

This optimization enables the Scheduler Service to process a minimum of 50 concurrent calls while maintaining system stability and reliability. By implementing asyncio-based concurrency at both the contact and batch levels, along with proper resource management and monitoring, the service can efficiently handle increased call volumes with minimal resource overhead.

The implementation is designed to be configurable, allowing for adjustment of concurrency limits based on available resources and external service capacities. Comprehensive monitoring and error handling ensure the system remains stable even under high load.
