# Batch Call Scheduler Service

## Overview

The Batch Call Scheduler Service is a system designed to automate outbound call scheduling and processing. It integrates with LiveKit for telephony functionality and uses APScheduler for reliable task scheduling. The service processes batch calls from a queue, manages outbound calling, and handles recording functionality.

## Features

- Automated batch call processing on a configurable schedule
- LiveKit integration for SIP telephony and call management
- CSV contact list processing for batch operations
- Call recording with GCP storage integration
- Robust error handling and logging
- Efficient asynchronous processing with asyncio

## Requirements

- Python 3.8+
- APScheduler
- LiveKit SDK
- gRPC
- pandas
- asyncio

## Installation

1. Clone the repository:
   ```bash
   git clone https://gitlab.rapidinnovation.tech/voice.ruh.ai/scheduler-service.git
   cd batch-call-scheduler
   ```

2. Install dependencies:
   ```bash
   poetry install
   ```

3. Configure environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration settings
   ```

## Configuration

The service requires the following configuration variables:

### Core Settings
- `CALL_SERVICE_HOST` - gRPC service host
- `CALL_SERVICE_PORT` - gRPC service port

### LiveKit Configuration
- `LIVEKIT_HOST_URL` - LiveKit server URL
- `LIVEKIT_API_KEY` - LiveKit API key
- `LIVEKIT_API_SECRET` - LiveKit API secret

### Storage Configuration
- `GCS_CRED` - GCP credentials
- `BUCKET_NAME` - GCP bucket name for recordings

## Usage

Start the scheduler service:

```bash
poetry run python -m app.main
```

The scheduler will:
1. Initialize connections to required services
2. Start APScheduler to run batch processing jobs
3. Process pending batch calls every 10 minutes
4. Maintain logs of all operations

## How It Works

1. **Scheduler Initialization**:
   - The service initializes connections to gRPC and LiveKit
   - APScheduler is configured to run batch processing at specified intervals

2. **Batch Call Processing**:
   - Retrieves pending batch calls from the call service
   - Downloads and processes contact lists from CSV files
   - Initiates outbound calls for each contact

3. **Call Management**:
   - Creates LiveKit rooms for each call
   - Sets up SIP connections for outbound dialing
   - Manages call metadata and status updates

4. **Recording**:
   - Sets up recording for each call using LiveKit's egress capability
   - Configures GCP storage for saving recordings

## Project Structure

```
batch-call-scheduler/
├── app/
│   ├── core/
│   │   └── config.py         # Configuration settings
│   ├── grpc/                 # gRPC protocol files
│   │   ├── call_pb2.py
│   │   └── call_pb2_grpc.py
├── services/
|   |-- scheduler_service.py  # Main scheduler implementation
├── main.py                   # Application entry point
├── pyproject.toml            # Project dependencies
└── README.md                 # This documentation
```

## Customization

### Changing the Schedule Interval

To modify how frequently batch calls are processed, adjust the interval in the `start()` method:

```python
self.scheduler.add_job(
    self.process_batch_calls,
    IntervalTrigger(minutes=10),  # Change to desired interval
    id='process_batch_calls',
    name='Process Batch Calls',
    replace_existing=True
)
```

### Adding Additional Jobs

You can add more scheduled jobs to handle different tasks:

```python
# Example: Add job to clean up completed calls daily
self.scheduler.add_job(
    self.cleanup_completed_calls,
    CronTrigger(hour=0, minute=0),  # Run at midnight
    id='cleanup_calls',
    name='Clean up Completed Calls',
    replace_existing=True
)
```

## Error Handling

The service includes comprehensive error handling:
- All operations are wrapped in try-except blocks
- Failed calls are properly logged and updated in the database
- Service continues operating even if individual calls fail

## Logging

The system uses Python's built-in logging module configured to output:
- INFO level for normal operations
- ERROR level for failures
- Detailed timestamps and context information

## License

[Your License Here]

## Contact

For questions or support, contact [your contact information].