# Batch Call Scheduler Service - Technical Requirements Document

## 1. System Architecture

### 1.1 Core Components
- Python 3.11+ based microservice
- APScheduler for job scheduling
- LiveKit for telephony integration
- gRPC for service communication
- GCP Storage for recording management

### 1.2 Dependencies
- APScheduler ^3.11.0
- LiveKit SDK ^0.22.0
- gRPC ^1.60.0
- pandas ^2.2.3
- pydantic ^2.5.3
- structlog ^24.1.0

## 2. Scheduler Core Requirements

### 2.1 APScheduler Configuration
- Implementation: AsyncIOScheduler
- Default interval: 5 minutes (configurable)
- Job persistence: Required
- Execution mode: Background
- Error recovery: Automatic job resumption

### 2.2 Job Management
```python
{
    "job_store": "default",
    "max_instances": 1,
    "coalesce": True,
    "misfire_grace_time": 300
}
```

## 3. LiveKit Integration Specifications

### 3.1 Room Management
- Dynamic room creation
- Unique room naming convention: `call_{timestamp}_{uuid}`
- Room lifetime: Auto-close after call completion
- Participant limit: 2 (caller + callee)

### 3.2 Recording Configuration
```python
{
    "audio_only": True,
    "format": "mp3",
    "sample_rate": 48000,
    "storage_path": "gs://{bucket_name}/recordings/{date}/{room_name}"
}
```

## 4. Contact Processing Requirements

### 4.1 CSV Format Specification
```csv
phone_number,name,metadata
+1234567890,John Doe,{"priority": "high"}
```

### 4.2 Validation Rules
- Phone number format: E.164
- Required fields: phone_number
- Maximum contacts per batch: 10,000
- Supported metadata fields: JSON object

## 5. Batch Processing Specifications

### 5.1 Batch Structure
```python
{
    "batch_id": "uuid",
    "status": "pending|processing|completed|failed",
    "total_contacts": int,
    "processed_contacts": int,
    "success_rate": float,
    "start_time": datetime,
    "end_time": datetime
}
```

### 5.2 Processing Rules
- Concurrent calls: Configurable (default: 10)
- Retry attempts: 3
- Retry delay: Exponential backoff
- Timeout: 30 seconds per call

## 6. Call Management Requirements

### 6.1 Call Metadata Structure
```python
{
    "call_id": "uuid",
    "room_name": str,
    "phone_number": str,
    "status": "initiating|connected|completed|failed",
    "duration": int,
    "recording_url": str,
    "trunk_id": str,
    "batch_id": str
}
```

### 6.2 SIP Configuration
- Protocol: SIP over UDP
- Codec: G.711
- DTMF mode: RFC2833
- Call timeout: 60 seconds

## 7. Error Handling Requirements

### 7.1 Error Categories
- Connection failures
- Validation errors
- Resource unavailability
- Timeout errors
- Authentication errors

### 7.2 Error Response Format
```python
{
    "error_code": str,
    "message": str,
    "timestamp": datetime,
    "context": dict,
    "retry_allowed": bool
}
```

## 8. Monitoring and Logging

### 8.1 Log Structure
```python
{
    "timestamp": datetime,
    "level": str,
    "event": str,
    "service": "scheduler-service",
    "trace_id": str,
    "data": dict
}
```

### 8.2 Metrics
- Call success rate
- Average call duration
- Batch processing time
- System resource usage
- API latency

## 9. Security Requirements

### 9.1 Authentication
- LiveKit API key/secret
- gRPC service authentication
- GCP service account

### 9.2 Data Protection
- Encryption at rest for recordings
- Secure credential storage
- Input sanitization
- Rate limiting per IP/client

## 10. Performance Requirements

### 10.1 System Metrics
- Maximum concurrent calls: 100
- Batch processing rate: 1000 contacts/minute
- API response time: <500ms
- CPU usage: <80%
- Memory usage: <2GB

### 10.2 Storage Requirements
- Recording retention: 30 days
- Log retention: 90 days
- Database backup: Daily

## 11. Integration Points

### 11.1 gRPC Service Interface
```protobuf
service CallService {
    rpc InitiateCall(CallRequest) returns (CallResponse);
    rpc UpdateCallStatus(StatusUpdate) returns (StatusResponse);
    rpc GetBatchStatus(BatchRequest) returns (BatchResponse);
}
```

### 11.2 LiveKit Integration
- Room management API
- Recording API
- SIP interface
- Webhook endpoints

## 12. Deployment Requirements

### 12.1 Container Specifications
- Base image: Python 3.11-slim
- Memory limit: 2GB
- CPU limit: 2 cores
- Health check endpoint: /health

### 12.2 Environment Variables
```env
LIVEKIT_URL=
LIVEKIT_API_KEY=
LIVEKIT_API_SECRET=
CALL_SERVICE_HOST=
CALL_SERVICE_PORT=
GCS_CRED=
BUCKET_NAME=
```

## 13. Testing Requirements

### 13.1 Test Coverage
- Minimum coverage: 80%
- Unit tests required for all core functions
- Integration tests for external services
- Load testing for performance validation

### 13.2 Test Scenarios
- Successful call flow
- Error handling
- Concurrent processing
- Resource cleanup
- Service recovery

## 14. Documentation Requirements

### 14.1 Required Documentation
- API documentation
- Setup guide
- Configuration guide
- Troubleshooting guide
- Architecture diagram

### 14.2 Code Documentation
- Function documentation
- Type hints
- Example usage
- Error handling documentation