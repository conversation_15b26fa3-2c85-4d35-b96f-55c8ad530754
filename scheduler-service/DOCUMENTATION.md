# Scheduler Service Documentation

## Overview
The Scheduler Service is a Python-based microservice designed to manage and execute batch call scheduling operations. It integrates with LiveKit for real-time communication and uses gRPC for service-to-service communication.

## Technical Stack
- **Language**: Python 3.11
- **Package Manager**: Poetry
- **Key Dependencies**:
  - APScheduler: For task scheduling
  - LiveKit SDK: Real-time communication
  - gRPC: Service communication
  - Pandas: Data processing
  - Pydantic: Data validation
  - Structlog: Structured logging

## Architecture

### Core Components

1. **Scheduler Engine**
   - Built using APScheduler
   - Handles batch processing jobs
   - Runs every 10 minutes to process pending calls
   - Manages job scheduling and execution

2. **gRPC Integration**
   - Communicates with other microservices
   - Generated stubs located in `app/grpc/`
   - Handles service-to-service communication

3. **LiveKit Integration**
   - Manages real-time communication
   - Handles call setup and management
   - Requires configuration via environment variables:
     - LIVEKIT_HOST_URL
     - LIVEKIT_API_KEY
     - LIVEKIT_API_SECRET

### Project Structure
```
scheduler-service/
├── app/
│   ├── main.py           # Application entry point
│   ├── grpc/            # Generated gRPC code
│   └── scripts/         # Utility scripts
├── tests/              # Test files
├── Dockerfile          # Container configuration
├── pyproject.toml     # Project dependencies and settings
└── poetry.lock        # Locked dependencies
```

## Setup and Installation

### Local Development
1. **Install Dependencies**:
   ```bash
   poetry install
   ```

2. **Environment Setup**:
   - Copy `.env.example` to `.env`
   - Configure required environment variables

3. **Generate gRPC Code**:
   ```bash
   poetry run python -m app.scripts.generate_grpc
   ```

4. **Run Service**:
   ```bash
   poetry run python -m app.main
   ```

### Docker Deployment
1. **Build Container**:
   ```bash
   docker build -t scheduler-service .
   ```

2. **Run Container**:
   ```bash
   docker run -p 50051:50051 scheduler-service
   ```

## Configuration

### Environment Variables
- `CALL_SERVICE_HOST`: gRPC service host
- `CALL_SERVICE_PORT`: gRPC service port
- `LIVEKIT_HOST_URL`: LiveKit server URL
- `LIVEKIT_API_KEY`: LiveKit API key
- `LIVEKIT_API_SECRET`: LiveKit API secret
- `GCS_CRED`: GCP credentials
- `BUCKET_NAME`: GCP bucket name for recordings

### Development Tools
- **Code Formatting**: Black (line length: 100)
- **Import Sorting**: isort
- **Type Checking**: mypy
- **Linting**: pylint
- **Testing**: pytest with asyncio support

## Development Guidelines

### Code Quality
- Type hints are mandatory (enforced by mypy)
- Follow Black code formatting
- Maintain test coverage (pytest-cov)
- Use async/await for asynchronous operations

### Testing
Run tests using:
```bash
poetry run pytest
```

### Error Handling
- All operations are wrapped in try-except blocks
- Failed operations are logged using structlog
- Service continues operating despite individual failures

## Logging
- Uses structlog for structured logging
- Log levels:
  - INFO: Normal operations
  - ERROR: Failures and exceptions
  - DEBUG: Detailed debugging information

## Security
- API authentication using tokens
- Secure communication via gRPC
- Environment variable management for sensitive data

## Monitoring and Maintenance
- Health checks available via API
- Error logging and tracking
- Performance metrics collection

## Build and Deployment

### Local Build
```bash
./run_local.sh
```

### Docker Build
```bash
docker build -t scheduler-service .
docker run scheduler-service
```

## Integration Points

### gRPC Services
- Call Service: Manages call operations
- Other microservices (as needed)

### External Services
- LiveKit: Real-time communication
- Google Cloud Storage: Recording storage

## Troubleshooting

### Common Issues
1. gRPC Connection Issues
   - Check service host/port configuration
   - Verify network connectivity

2. LiveKit Integration
   - Verify API credentials
   - Check LiveKit server availability

3. Scheduler Issues
   - Check job configurations
   - Verify database connectivity

## Future Improvements
1. Enhanced monitoring and metrics
2. Improved error recovery
3. Additional scheduling features
4. Performance optimizations

## Support and Contact
For issues and support:
1. Check existing documentation
2. Review error logs
3. Contact the development team