

include:
  # Include templates from the centralized GitLab CI repository
  - project: "devops/gitlab-ci" # Replace with your actual GitLab CI repo path
    ref: main
    file:
      - "templates/setup_template.yml"
      - "templates/build_template.yml"
      - "templates/deploy_template.yml"
      - "templates/notify_template.yml"

stages:
  - setup
  - build
  - notify_build
  - deploy
  - notify_final

# Setup stage - prepares authentication
setup:
  extends: .setup_template
  variables:
    GOOGLE_SERVICE_KEY: $SERVICE_ACCOUNT_KEY
  rules:
    - if: $CI_COMMIT_REF_NAME == "dev" || $CI_COMMIT_REF_NAME == "staging" || $CI_COMMIT_REF_NAME == "main"

# Build stage - builds Docker image from your app's Dockerfile
build:
  extends: .build_template
  variables:
    # Override these variables for your specific application:
    GOOGLE_SERVICE_KEY: $SERVICE_ACCOUNT_KEY
    GAR_HOSTNAME: "us-central1-docker.pkg.dev"
    PROJECT_ID: "$PROJECT_ID"
    REPOSITORY: "ruh-ai"
    IMAGE_NAME: "$CI_PROJECT_NAME"
    REPO_URL: $REPO_URL
  rules:
    - if: $CI_COMMIT_REF_NAME == "dev" || $CI_COMMIT_REF_NAME == "staging" || $CI_COMMIT_REF_NAME == "main"

# Notification for build failures
slack_notification_build:
  extends: .slack_notification_build
  only:
    - dev
    - staging
    - main
  when: on_failure

# Deploy stage - uses K8s manifest template from centralized repo
deploy:
  extends: .deploy_template
  variables:
    MEMORY_REQUEST: "64Mi"
    CPU_REQUEST: "50m"
    MEMORY_LIMIT: "800Mi"
    CPU_LIMIT: "250m"
    CONTAINER_PORT: "50054"
    SERVICE_PORT: "80"
    NAMESPACE: "voice-ruh-ai-$CI_COMMIT_REF_NAME"
    SERVICE_NAME: "scheduler-service-api"
    REPOSITORY: "ruh-ai"
  rules:
    - if: $CI_COMMIT_REF_NAME == "dev" || $CI_COMMIT_REF_NAME == "staging" || $CI_COMMIT_REF_NAME == "main"


