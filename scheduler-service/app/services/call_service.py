import asyncio
import grpc
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional

from app.grpc import call_pb2
from app.grpc import call_pb2_grpc

logger = logging.getLogger(__name__)

class CallService:
    def __init__(self, host: str, port: int):
        self.host = host
        self.port = port
        self.channel = grpc.insecure_channel(f"{host}:{port}")
        self.stub = call_pb2_grpc.CallServiceStub(self.channel)

    async def get_all_batch_calls(self, page: int = 1, page_size: int = 1):
        """Fetch pending batch calls from the call service"""
        loop = asyncio.get_running_loop()
        try:
            request = call_pb2.GetAllBatchCallsRequest(
                page=page,
                pageSize=page_size
            )
            logger.info(f"Fetching batch calls (page {page}, size {page_size})...")
            
            response = await loop.run_in_executor(
                None, lambda: self.stub.getAllBatchCalls(request)
            )
            
            logger.info(f"Received {len(response.data) if response.data else 0} batch calls.")
            return response.data
        except grpc.RpcError as rpc_error:
            logger.error(f"gRPC error fetching batch calls: {rpc_error.code()} - {rpc_error.details()}")
            raise
        except Exception as e:
            logger.error(f"Error fetching batch calls: {e}", exc_info=True)
            raise

    async def get_contact_by_id(self, contact_id: str):
        """Fetch contact details by ID from the call service"""
        loop = asyncio.get_running_loop()
        try:
            request = call_pb2.GetContactByIdRequest(contact_id=contact_id)
            logger.info(f"Fetching contact details for contact_id: {contact_id}")
            
            response = await loop.run_in_executor(
                None, lambda: self.stub.getContactById(request)
            )
            
            return response
        except grpc.RpcError as rpc_error:
            logger.error(f"gRPC error fetching contact: {rpc_error.code()} - {rpc_error.details()}")
            raise
        except Exception as e:
            logger.error(f"Error fetching contact details: {e}", exc_info=True)
            raise

    async def get_contact_lists(self, contact_id: str, page: int = 1, page_size: int = 100):
        """Fetch contact lists from the call service"""
        loop = asyncio.get_running_loop()
        try:
            request = call_pb2.GetContactListsRequest(
                contact_id=contact_id,
                page=page,
                pageSize=page_size
            )
            
            response = await loop.run_in_executor(
                None, lambda: self.stub.getContactLists(request)
            )
            
            return response
        except grpc.RpcError as rpc_error:
            logger.error(f"gRPC error fetching contact lists: {rpc_error.code()} - {rpc_error.details()}")
            raise
        except Exception as e:
            logger.error(f"Error fetching contact lists: {e}", exc_info=True)
            raise

    async def get_agents_by_id(self, agent_id: str):
        """Fetch agents by contact ID from the call service"""
        loop = asyncio.get_running_loop()
        try:
            request = call_pb2.GetAgentDetailsRequest(agent_id=agent_id)
            logger.info(f"Fetching agents for contact_id: {agent_id}")
            
            response = await loop.run_in_executor(
                None, lambda: self.stub.getAgentDetails(request)
                )
            logger.info(f"Received agent details: {response}")

            return response
        except grpc.RpcError as rpc_error:
            logger.error(f"gRPC error fetching agents: {rpc_error.code()} - {rpc_error.details()}")
            raise
        except Exception as e:
            logger.error(f"Error fetching agents: {e}", exc_info=True)
            raise


    async def store_call_details(self, 
                                batch_call_id: str,
                                user_id: str,
                                phone_number: str,
                                client_name: str,
                                csv_metadata: Dict[str, Any],
                                additional_guidelines: str = "",
                                agent_id: Optional[str] = None):
        """Store initial call details in the call service"""
        loop = asyncio.get_running_loop()
        try:
            metadata_dict = {
                "csv_data": csv_metadata,
                "clientName": client_name,
            }
            metadata_json = json.dumps(metadata_dict)
            
            request = call_pb2.StoreCallDetailsRequest(
                batch_call_id=str(batch_call_id),
                user_id=str(user_id) if user_id else "",
                call_duration=0,
                recording_url="",
                transcription_url="",
                status="pending",
                call_failing_reason="",
                type="outbound",
                from_phone="",
                to_phone=str(phone_number),
                transfer_status="false",
                additional_guidelines=additional_guidelines or "",
                call_metadata=metadata_json,
                month=datetime.utcnow().month,
                year=datetime.utcnow().year,
                billed=False,
                invoice_id="",
                agent_id=str(agent_id) if agent_id else ""
            )
            
            response = await loop.run_in_executor(
                None, lambda: self.stub.storeCallDetails(request)
            )
            
            if not response or not response.success or not response.call_id:
                error_msg = response.message if response else 'No response'
                logger.error(f"Failed to store call details: {error_msg}")
                return None
                
            logger.info(f"Call details stored. Call ID: {response.call_id}")
            return response.call_id
            
        except grpc.RpcError as rpc_error:
            logger.error(f"gRPC error storing call details: {rpc_error.code()} - {rpc_error.details()}")
            return None
        except Exception as e:
            logger.error(f"Error storing call details: {e}", exc_info=True)
            return None

    async def update_call_details(self, call_id: str, status: str, call_failing_reason: str = ""):
        """Update call details in the call service"""
        loop = asyncio.get_running_loop()
        try:
            request = call_pb2.UpdateCallDetailsRequest(
                call_id=call_id,
                status=status,
                call_failing_reason=call_failing_reason[:1000] if call_failing_reason else ""
            )
            
            await loop.run_in_executor(
                None, lambda: self.stub.updateCallDetails(request)
            )
            
            logger.info(f"[{call_id}] Call details updated to status: {status}")
            return True
        except grpc.RpcError as rpc_error:
            logger.error(f"[{call_id}] gRPC error updating call details: {rpc_error.code()} - {rpc_error.details()}")
            return False
        except Exception as e:
            logger.error(f"[{call_id}] Error updating call details: {e}", exc_info=True)
            return False

    async def update_batch_status(self, batch_call_id: str, status: str):
        """Update batch call status in the call service"""
        loop = asyncio.get_running_loop()
        try:
            request = call_pb2.UpdateBatchCallRequest(
                batch_call_id=batch_call_id,
                status=status,
            )
            
            response = await loop.run_in_executor(
                None, lambda: self.stub.updateBatchCall(request)
            )
            
            if not response.success:
                logger.error(f"[{batch_call_id}] Failed to update batch call status: {response.message}")
                return False
                
            logger.info(f"[{batch_call_id}] Batch status updated to: {status}")
            return True
        except grpc.RpcError as rpc_error:
            logger.error(f"[{batch_call_id}] gRPC error updating batch status: {rpc_error.code()} - {rpc_error.details()}")
            return False
        except Exception as e:
            logger.error(f"[{batch_call_id}] Error updating batch status: {e}", exc_info=True)
            return False

    def close(self):
        """Close the gRPC channel"""
        if self.channel:
            self.channel.close()
            logger.info("gRPC channel closed.")