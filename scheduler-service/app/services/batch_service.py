import json
import logging
import pandas as pd

from app.grpc import call_pb2
from app.services.call_service import CallService
from app.services.livekit_service import LiveKitService
from app.core.config import settings


logger = logging.getLogger(__name__)

class BatchService:
    def __init__(self, call_service: CallService, livekit_service: LiveKitService):
        self.call_service = call_service
        self.livekit_service = livekit_service

    async def process_batch_calls(self):
        """Process pending batch calls from the queue"""
        try:
            batch_calls = await self.call_service.get_all_batch_calls(page=1, page_size=1)
            
            if not batch_calls:
                logger.info("No pending batch calls found to process.")
                return

            for batch_call in batch_calls:
                logger.info(f"Processing batch call ID: {batch_call.id}, Title: {batch_call.title}")
                await self.process_single_batch_call(batch_call)

        except Exception as e:
            logger.error(f"Error processing batch calls: {e}", exc_info=True)

    async def process_single_batch_call(self, batch_call: call_pb2.BatchCall):
        """Process a single batch call campaign"""
        batch_call_id = batch_call.id
        
        try:
            # Fetch contact details
            contact = await self.call_service.get_contact_by_id(batch_call.contact_id)
            
            if not contact:
                logger.error(f"[{batch_call_id}] Contact details not found for contact_id {batch_call.contact_id}.")
                await self.call_service.update_batch_status(batch_call_id, "failed")
                return

            logger.info(f"[{batch_call_id}] Contact found. Agent ID: {contact.agent_id}. Fetching contact lists...")

            # Get all contacts with pagination
            all_contacts = []
            current_page = 1
            page_size = 100
            
            while True:
                contact_lists_response = await self.call_service.get_contact_lists(
                    batch_call.contact_id, current_page, page_size
                )
                
                if not contact_lists_response.data:
                    break
                    
                all_contacts.extend(contact_lists_response.data)
                
                if current_page >= contact_lists_response.metadata.totalPages:
                    break
                    
                current_page += 1

            if not all_contacts:
                logger.warning(f"[{batch_call_id}] No contacts found. Marking batch as completed.")
                await self.call_service.update_batch_status(batch_call_id, "ended")
                return

            logger.info(f"[{batch_call_id}] Found {len(all_contacts)} contacts.")

            # Process each contact
            all_calls_initiated = True
            for index, contact_item in enumerate(all_contacts):
                if not contact_item.is_valid:
                    logger.warning(f"[{batch_call_id}] Skipping invalid contact with phone: {contact_item.phone_number}")
                    continue

                logger.info(f"[{batch_call_id}] Processing contact {index+1}/{len(all_contacts)}")
                try:
                    # Create contact data dictionary
                    contact_data = {'phone_number': contact_item.phone_number}
                    
                    try:
                        metadata = json.loads(contact_item.csv_metadata)
                        contact_data.update(metadata)
                    except json.JSONDecodeError as e:
                        logger.warning(f"[{batch_call_id}] Failed to parse csv_metadata for contact {index+1}: {e}")
                        contact_data['Name'] = 'Customer'  # Default value
                    
                    contact_row = pd.Series(contact_data)
                    
                    call_initiated = await self.initiate_outbound_call(batch_call, contact, contact_row)
                    if not call_initiated:
                        all_calls_initiated = False
                except Exception as call_exc:
                    logger.error(f"[{batch_call_id}] Error initiating call for contact {index+1}: {call_exc}", exc_info=True)
                    all_calls_initiated = False

            # Update batch status based on results
            if all_calls_initiated:
                await self.call_service.update_batch_status(batch_call_id, "complete")
            else:
                await self.call_service.update_batch_status(batch_call_id, "failed")

        except Exception as e:
            logger.error(f"[{batch_call_id}] Error processing batch call: {e}", exc_info=True)
            await self.call_service.update_batch_status(batch_call_id, "failed")

    async def initiate_outbound_call(self, batch_call: call_pb2.BatchCall, contact: call_pb2.GetContactByIdResponse, contact_row: pd.Series):
        """Initiate an outbound call for a single contact"""
        try:
            phone_number = contact_row.get('phone_number')
            if not phone_number or pd.isna(phone_number):
                logger.warning(f"[{batch_call.id}] No phone number found. Skipping.")
                return False
                
            phone_number = str(phone_number).strip()
            name = str(contact_row.get('Name', 'customer')).strip()

            # Create CSV metadata dictionary
            csv_metadata = {}
            for col, value in contact_row.items():
                csv_metadata[col] = str(value).strip()

            user_id = batch_call.user_id
            agent_id = contact.agent_id if contact.agent_id else None

            # Store initial call details
            call_id = await self.call_service.store_call_details(
                batch_call_id=batch_call.id,
                user_id=user_id,
                phone_number=phone_number,
                client_name=name,
                csv_metadata=csv_metadata,
                additional_guidelines=batch_call.additional_guidelines,
                agent_id=agent_id
            )
            
            if not call_id:
                logger.error(f"[{batch_call.id}] Failed to store call details. Skipping call to {phone_number}.")
                return False
            
            agent_data = await self.call_service.get_agents_by_id(agent_id)
            system_prompt = agent_data.system_prompt

            # Room name and metadata
            room_name = f"{phone_number}-{call_id}"
            livekit_metadata = {
                'call_id': call_id,
                'name': name,
                'transfer_to': batch_call.transfer_to or None,
                'additional_guidelines': batch_call.additional_guidelines or None,
                'phone_number': phone_number,
                "system_prompt": system_prompt or None
            }
            livekit_metadata = {k: v for k, v in livekit_metadata.items() if v is not None}
            
            try:
                # Create or update room
                await self.livekit_service.create_or_update_room(room_name, livekit_metadata)
                
                # Create SIP participant
                await self.livekit_service.create_sip_participant(
                    settings.TRUNCK_ID, phone_number, room_name, livekit_metadata
                )
    
                return True
            except Exception as livekit_err:
                error_msg = str(livekit_err)
                logger.error(f"[{batch_call.id}][{call_id}] LiveKit error: {error_msg}")
                await self.call_service.update_call_details(call_id, "failed", f"LiveKit Error: {error_msg}")
                return False
                
        except Exception as e:
            logger.error(f"[{batch_call.id}] Error during outbound call initiation: {e}", exc_info=True)
            return False