import asyncio
import logging
from datetime import datetime

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger

from app.services.batch_service import BatchService
from app.services.call_service import CallService
from app.services.livekit_service import LiveKitService

logger = logging.getLogger(__name__)

class SchedulerService:
    def __init__(self, livekit_host_url: str, livekit_api_key: str, livekit_api_secret: str, bucket_name: str, call_service_host: str, call_service_port: int):
        self.scheduler = AsyncIOScheduler()
        
        # Initialize services
        self.livekit_service = LiveKitService(
            livekit_host_url=livekit_host_url,
            livekit_api_key=livekit_api_key,
            livekit_api_secret=livekit_api_secret
        )
        
        self.call_service = CallService(
            host=call_service_host,
            port=call_service_port
        )
        
        self.batch_service = BatchService(
            call_service=self.call_service,
            livekit_service=self.livekit_service,
        )

    async def initialize(self):
        """Initialize all services"""
        try:
            await self.livekit_service.initialize()
            logger.info("LiveKit service initialized.")
        except Exception as e:
            logger.error(f"Failed to initialize LiveKit service: {e}", exc_info=True)
            raise

    def start(self):
        """Start the scheduler"""
        logger.info("Initializing Scheduler...")

        interval_minutes = 5
        self.scheduler.add_job(
            self.batch_service.process_batch_calls,
            IntervalTrigger(minutes=interval_minutes),
            id='process_batch_calls',
            name='Process Batch Calls',
            replace_existing=True,
            next_run_time=datetime.now()
        )

        self.scheduler.start()
        logger.info(f"Scheduler started. Batch call processing scheduled every {interval_minutes} minute(s).")

    def shutdown(self):
        """Shutdown the scheduler and close connections"""
        if self.scheduler.running:
            self.scheduler.shutdown()
            logger.info("Scheduler shutdown")
            
        self.call_service.close()

    async def run(self):
        """Main run method"""
        logger.info("Starting Scheduler Service...")
        try:
            await self.initialize()
            
            self.start()
            
            # Keep the service running
            while True:
                await asyncio.sleep(3600)
                
        except (KeyboardInterrupt, SystemExit):
            logger.info("Shutdown signal received.")
        finally:
            self.shutdown()
            logger.info("Scheduler Service stopped.")