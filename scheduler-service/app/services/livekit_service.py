import base64
import json
import logging
import os
from typing import Dict, Any

from livekit import api

logger = logging.getLogger(__name__)

class LiveKitService:
    def __init__(self, livekit_host_url: str, livekit_api_key: str, livekit_api_secret: str):
        self.livekit_host_url = livekit_host_url
        self.livekit_api_key = livekit_api_key
        self.livekit_api_secret = livekit_api_secret
        self.livekit_api = None

    async def initialize(self):
        """Initialize LiveKit API client"""
        if self.livekit_api is None:
            self.livekit_api = api.LiveKitAPI(
                url=self.livekit_host_url,
                api_key=self.livekit_api_key,
                api_secret=self.livekit_api_secret
            )

    async def create_or_update_room(self, room_name: str, metadata: Dict[str, Any]):
        """Create or update a LiveKit room with metadata"""
        if self.livekit_api is None:
            await self.initialize()

        metadata_str = json.dumps(metadata)
        try:
            logger.info(f"Creating room '{room_name}' with metadata: {metadata_str}")
            await self.livekit_api.room.create_room(
                api.CreateRoomRequest(
                    name=room_name,
                    metadata=metadata_str
                )
            )
            logger.info(f"Room '{room_name}' created successfully.")
            return True
        except Exception as e:
            if 'room already exists' in str(e).lower():
                logger.warning(f"Room '{room_name}' already exists. Updating metadata.")
                await self.livekit_api.room.update_room_metadata(
                    api.UpdateRoomMetadataRequest(
                        room=room_name,
                        metadata=metadata_str
                    )
                )
                logger.info(f"Metadata updated for room '{room_name}'.")
                return True
            else:
                logger.error(f"Failed to create or update room '{room_name}': {e}")
                raise

    async def create_sip_participant(self, trunk_id: str, phone_number: str, room_name: str, metadata: Dict[str, Any]):
        """Create a SIP participant for outbound calling"""
        if self.livekit_api is None:
            await self.initialize()

        participant_identity = f'sip-{phone_number}'
        participant_name = f'PSTN-{phone_number}'
        metadata_str = json.dumps(metadata)

        sip_request = api.CreateSIPParticipantRequest(
            sip_trunk_id=trunk_id,
            sip_call_to=phone_number,
            room_name=room_name,
            participant_identity=participant_identity,
            participant_name=participant_name,
            participant_metadata=metadata_str,
        )
        
        logger.info(f"Creating SIP participant for {phone_number} in room '{room_name}' with identity '{participant_identity}'")
        participant = await self.livekit_api.sip.create_sip_participant(sip_request)
        logger.info(f"SIP participant created successfully: {participant}")
        return participant

    async def update_room_metadata(self, room_name: str, metadata: Dict[str, Any]):
        """Update room metadata"""
        if self.livekit_api is None:
            await self.initialize()

        metadata_str = json.dumps(metadata)
        await self.livekit_api.room.update_room_metadata(
            api.UpdateRoomMetadataRequest(
                room=room_name,
                metadata=metadata_str
            )
        )
        logger.info(f"Room metadata updated for room '{room_name}'.")