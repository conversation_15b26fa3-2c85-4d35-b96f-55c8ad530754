from typing import Any, Dict, Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # Application settings
    ENV: str = "dev"
    APP_NAME: str = "scheduler-service"
    DEBUG: bool = False
    PORT: int = 50052

    REPO_URL: str = ""
    GIT_TOKEN: str = ""

    API_GATEWAY_URL: str = ""

    LIVEKIT_URL:str=""
    LIVEKIT_API_KEY:str=""
    LIVEKIT_API_SECRET:str=""

    TRUNCK_ID:str=""

    GCS_CRED: str = ""
    BUCKET_NAME: str = ""

    CALL_SERVICE_HOST: str = "call_service"
    CALL_SERVICE_PORT: int = 50058

    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()
