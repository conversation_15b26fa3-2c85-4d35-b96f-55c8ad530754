import asyncio
import logging
from app.core.config import settings
from app.services.scheduler_service import SchedulerService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def main():
    try:
        # Initialize the scheduler service with all required configuration
        scheduler = SchedulerService(
            livekit_host_url=settings.LIVEKIT_URL,
            livekit_api_key=settings.LIVEKIT_API_KEY,
            livekit_api_secret=settings.LIVEKIT_API_SECRET,
            bucket_name=settings.BUCKET_NAME,
            call_service_host=settings.CALL_SERVICE_HOST,
            call_service_port=settings.CALL_SERVICE_PORT
        )
        
        # Run the scheduler
        logger.info("Starting the scheduler service...")
        await scheduler.run()
    except Exception as e:
        logger.error(f"Error in main: {e}", exc_info=True)
        raise

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Application shutdown by user")
    except Exception as e:
        logger.error(f"Unhandled exception: {e}", exc_info=True)