apiVersion: v1
kind: ServiceAccount
metadata:
  name: scheduler-service-ai-sa
  namespace: ruh-ai-dev
  labels:
    name: scheduler-service-ai-sa
    namespace: ruh-ai-dev
    app: scheduler-service-ai
    deployment: scheduler-service-ai-dp
---
# Create Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: scheduler-service-ai-dp
  namespace: ruh-ai-dev
  labels:
    name: scheduler-service-ai-dp
    namespace: ruh-ai-dev
    app: scheduler-service-ai
    serviceaccount: scheduler-service-ai-sa
    deployment: scheduler-service-ai-dp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: scheduler-service-ai
      deployment: scheduler-service-ai-dp
  template:
    metadata:
      labels:
        namespace: ruh-ai-dev
        app: scheduler-service-ai
        deployment: scheduler-service-ai-dp
    spec:
      serviceAccountName: scheduler-service-ai-sa      
      containers:
      - name: scheduler-service-ai
        image: us-central1-docker.pkg.dev/<PROJECT_ID>/<REPOSITORY>/<IMAGE_NAME>:<VERSION>
        resources:
          requests:
            memory: 64Mi
            cpu: 50m
          limits:
            memory: 1024Mi
            cpu: 250m
        ports:
        - containerPort: 50054
      #   readinessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 5
      #     periodSeconds: 10
      #   livenessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 15
      #     periodSeconds: 20
      # tolerations:
      # - key: "spotInstance"
      #   operator: "Equal"
      #   value: "true"
      #   effect: "PreferNoSchedule"
      # nodeSelector:    
      #   eks.amazonaws.com/capacityType: SPOT       
---
#### Create Service
apiVersion: v1
kind: Service
metadata:
  name: scheduler-service-ai-svc
  namespace: ruh-ai-dev
spec:
  selector:
    app: scheduler-service-ai
    deployment: scheduler-service-ai-dp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 50054
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 100000
---
### Create HPA
# apiVersion: autoscaling/v2beta1
# kind: HorizontalPodAutoscaler
# metadata:
#   name: scheduler-service-user-hpa
#   namespace: ruh-ai-dev
# spec:
#   scaleTargetRef:
#     apiVersion: apps/v1
#     kind: Deployment
#     name: scheduler-service-user-dp
#   minReplicas: 1
#   maxReplicas: 2
#   metrics:
#     - type: Resource
#       resource:
#         name: cpu
#         targetAverageUtilization: 60
---
### Create Nginx Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: scheduler-service-user-ingress
  namespace: ruh-ai-dev
spec:
  ingressClassName: nginx
  rules:
  - host: scheduler-service.rapidinnovation.dev
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: scheduler-service-ai-svc
            port:
              number: 80




