[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "scheduler-service"
version = "0.1.0"
description = "Standalone scheduler service for workflow automation"
authors = ["Scheduler Service Team <<EMAIL>>"]
readme = "README.md"
packages = [{include = "scheduler_service", from = "src"}]

[tool.poetry.dependencies]
python = ">=3.9,<3.13"
sqlalchemy = "^2.0.29"
alembic = "1.12.1"

pydantic = "^2.7.1"
pydantic-settings = "^2.2.1"
celery = "5.3.4"
redis = "5.0.1"
httpx = "0.25.2"
structlog = "23.2.0"
python-dateutil = "2.8.2"
asyncpg = "^0.30.0"
python-dotenv = "^1.1.0"
greenlet = "^3.2.3"
pytz = "^2024.1"
croniter = "^1.4.1"
fastapi = "0.104.1"
uvicorn = {extras = ["standard"], version = "0.24.0"}
aiohttp = "^3.9.0"

[tool.poetry.group.dev.dependencies]
pytest = "7.4.3"
pytest-asyncio = "0.21.1"
pytest-cov = "4.1.0"
pytest-mock = "3.12.0"
black = "23.11.0"
isort = "5.12.0"
flake8 = "6.1.0"
mypy = "1.7.1"
types-redis = "********"
types-python-dateutil = "*********"
pre-commit = "3.5.0"
ipython = "8.17.2"
ipdb = "0.13.13"

[tool.poetry.scripts]
scheduler-service = "scheduler_service.main:main"
run-scheduler = "scheduler_service.cli:run_scheduler"

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["scheduler_service"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "celery.*",
    "redis.*",
    "croniter.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--cov=scheduler_service",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "e2e: End-to-end tests",
    "slow: Slow running tests",
]
