"""
Authentication client for the Scheduler Service.

This module provides authentication functionality including bearer token
validation with the external auth service.
"""

import httpx
from typing import Dict, Any, Optional

from src.utils.config import get_settings
from src.utils.logger import get_logger

logger = get_logger(__name__)


class AuthServiceError(Exception):
    """Base exception for auth service errors."""

    pass


class AuthServiceConnectionError(AuthServiceError):
    """Exception raised when unable to connect to auth service."""

    pass


class AuthClient:
    """
    Client for communicating with the authentication service.

    Provides methods for validating bearer tokens and retrieving user information.
    """

    def __init__(self):
        """Initialize the auth client."""
        self.settings = get_settings()
        self.base_url = self.settings.auth_service_url.rstrip("/")
        self.timeout = self.settings.auth_service_timeout
        self._client: Optional[httpx.AsyncClient] = None

    async def __aenter__(self):
        """Async context manager entry."""
        self._client = httpx.AsyncClient(
            timeout=httpx.Timeout(self.timeout),
            headers={
                "User-Agent": "scheduler-service/0.1.0",
                "Accept": "application/json",
            },
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self._client:
            await self._client.aclose()
            self._client = None

    async def validate_bearer_token(
        self, bearer_token: str
    ) -> Optional[Dict[str, Any]]:
        """
        Validate a bearer token with the auth service.

        Args:
            bearer_token: The bearer token to validate

        Returns:
            Dict containing user details if token is valid, None otherwise

        Raises:
            AuthServiceError: If there's an error communicating with the auth service
        """
        try:
            logger.debug("Validating bearer token with auth service")

            if not self._client:
                raise AuthServiceError(
                    "Auth client not initialized. Use async context manager."
                )

            # Make request to auth service /users/me endpoint
            response = await self._client.get(
                f"{self.base_url}/api/v1/users/me",
                headers={
                    "Authorization": f"Bearer {bearer_token}",
                    "accept": "application/json",
                },
            )

            if response.status_code == 200:
                user_data = response.json()
                logger.info(
                    "Successfully validated bearer token",
                    user_id=user_data.get("id"),
                    email=user_data.get("email"),
                )
                return user_data
            elif response.status_code == 401:
                logger.warning("Invalid bearer token provided")
                return None
            else:
                logger.error(
                    "Unexpected response from auth service",
                    status_code=response.status_code,
                    response_text=response.text,
                )
                raise AuthServiceError(
                    f"Auth service returned status {response.status_code}"
                )

        except httpx.ConnectError as e:
            logger.error("Failed to connect to auth service", error=str(e))
            error_msg = "Unable to connect to authentication service. "
            if self.base_url:
                error_msg += f"- Current auth service URL: {self.base_url}"
            raise AuthServiceConnectionError(error_msg)
        except httpx.TimeoutException as e:
            logger.error("Auth service request timed out", error=str(e))
            raise AuthServiceError("Authentication service request timed out")
        except httpx.RequestError as e:
            logger.error("Request error when contacting auth service", error=str(e))
            error_msg = "Error communicating with authentication service. "
            if self.base_url:
                error_msg += f"- Current auth service URL: {self.base_url}"
            raise AuthServiceConnectionError(error_msg)
        except Exception as e:
            logger.error("Unexpected error validating bearer token", error=str(e))
            raise AuthServiceError(f"Unexpected error: {str(e)}")

    async def health_check(self) -> bool:
        """
        Check if the auth service is healthy and accessible.

        Returns:
            bool: True if auth service is healthy
        """
        try:
            if not self._client:
                raise AuthServiceError(
                    "Auth client not initialized. Use async context manager."
                )

            response = await self._client.get(f"{self.base_url}/health")
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Auth service health check failed: {e}")
            return False


# Singleton instance for easy access
_auth_client_instance: Optional[AuthClient] = None


def get_auth_client() -> AuthClient:
    """
    Get a singleton instance of the auth client.

    Returns:
        AuthClient: The auth client instance
    """
    global _auth_client_instance
    if _auth_client_instance is None:
        _auth_client_instance = AuthClient()
    return _auth_client_instance


async def validate_bearer_token(bearer_token: str) -> Optional[Dict[str, Any]]:
    """
    Convenience function to validate a bearer token.

    Args:
        bearer_token: The bearer token to validate

    Returns:
        Dict containing user details if token is valid, None otherwise
    """
    async with get_auth_client() as client:
        return await client.validate_bearer_token(bearer_token)
