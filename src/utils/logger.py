"""
Structured logging configuration for the Scheduler Service.

This module provides structured JSON logging with correlation IDs for
request tracing and proper log formatting for both development and production.
"""

import logging
import sys
import uuid
from contextvars import Context<PERSON><PERSON>
from typing import Any, Dict, Optional

import structlog
from structlog.types import EventDict, Processor


# Context variable for correlation ID
correlation_id_var: ContextVar[Optional[str]] = ContextVar(
    "correlation_id", default=None
)

# Flag to track if logging has been configured
_logging_configured = False


def add_correlation_id(
    logger: Any, method_name: str, event_dict: EventDict
) -> EventDict:
    """
    Add correlation ID to log events.

    Args:
        logger: Logger instance
        method_name: Method name being called
        event_dict: Event dictionary to modify

    Returns:
        EventDict: Modified event dictionary with correlation ID
    """
    correlation_id = correlation_id_var.get()
    if correlation_id:
        event_dict["correlation_id"] = correlation_id
    return event_dict


def add_service_info(logger: Any, method_name: str, event_dict: EventDict) -> EventDict:
    """
    Add minimal service information to log events.
    Only add service info for ERROR and CRITICAL levels to reduce noise.

    Args:
        logger: Logger instance
        method_name: Method name being called
        event_dict: Event dictionary to modify

    Returns:
        EventDict: Modified event dictionary with service info
    """
    # Only add service info for important logs
    level = event_dict.get("level", "").upper()
    if level in ["ERROR", "CRITICAL"]:
        event_dict["service"] = "scheduler-service"
        event_dict["version"] = "0.1.0"
    return event_dict


def filter_sensitive_data(
    logger: Any, method_name: str, event_dict: EventDict
) -> EventDict:
    """
    Filter sensitive data from log events.

    Args:
        logger: Logger instance
        method_name: Method name being called
        event_dict: Event dictionary to modify

    Returns:
        EventDict: Modified event dictionary with sensitive data filtered
    """
    sensitive_keys = {
        "password",
        "token",
        "secret",
        "key",
        "credential",
        "authorization",
        "auth",
        "api_key",
        "access_token",
        "refresh_token",
        "client_secret",
    }

    def filter_dict(data: Any) -> Any:
        """Recursively filter sensitive data from dictionary."""
        if isinstance(data, dict):
            filtered = {}
            for key, value in data.items():
                if isinstance(key, str) and any(
                    sensitive in key.lower() for sensitive in sensitive_keys
                ):
                    filtered[key] = "[REDACTED]"
                elif isinstance(value, dict):
                    filtered[key] = filter_dict(value)
                elif isinstance(value, list):
                    filtered[key] = [
                        filter_dict(item) if isinstance(item, dict) else item
                        for item in value
                    ]
                else:
                    filtered[key] = value
            return filtered
        return data

    # Filter the entire event dict
    return filter_dict(event_dict)


def clean_console_renderer(_, __, event_dict):
    """
    Clean console renderer for minimal, readable logs.
    """
    import datetime

    # Extract key information
    timestamp = datetime.datetime.now().strftime("%H:%M:%S")
    level = event_dict.pop("level", "INFO").upper()
    event = event_dict.pop("event", "")
    logger_name = event_dict.pop("logger", "").split(".")[-1]  # Only last part

    # Remove noise fields
    noise_fields = {
        "service",
        "version",
        "correlation_id",
        "timestamp",
        "status",
        "updated",
    }
    for field in noise_fields:
        event_dict.pop(field, None)

    # Color codes for different log levels
    colors = {
        "DEBUG": "\033[90m",  # Dark gray
        "INFO": "\033[32m",  # Green
        "WARNING": "\033[33m",  # Yellow
        "ERROR": "\033[31m",  # Red
        "CRITICAL": "\033[35m",  # Magenta
    }
    reset = "\033[0m"

    # Skip debug logs for certain noisy components
    if level == "DEBUG":
        noisy_loggers = {
            "distributed_lock",
            "scheduler_manager",
            "instance_coordinator",
            "scheduler_engine",
        }
        if any(noisy in logger_name.lower() for noisy in noisy_loggers):
            # Only show important debug messages
            important_keywords = {
                "error",
                "failed",
                "success",
                "created",
                "updated",
                "deleted",
                "scheduled",
                "executed",
                "queued",
            }
            if not any(keyword in event.lower() for keyword in important_keywords):
                return None  # Skip this log

    # Format the main log line
    color = colors.get(level, "")

    # Compact format
    if level == "INFO":
        main_line = f"{color}[{timestamp}] {event}{reset}"
    else:
        main_line = f"{color}[{timestamp}] {level} {event}{reset}"

    # Add important context only
    context_parts = []

    # Add key identifiers with truncation
    if "scheduler_id" in event_dict:
        scheduler_id = str(event_dict.pop("scheduler_id"))
        context_parts.append(f"scheduler={scheduler_id[:8]}...")

    if "user_id" in event_dict:
        user_id = str(event_dict.pop("user_id"))
        context_parts.append(f"user={user_id[:8]}...")

    if "frequency" in event_dict:
        context_parts.append(f"freq={event_dict.pop('frequency')}")

    if "scheduler_name" in event_dict:
        context_parts.append(f"name={event_dict.pop('scheduler_name')}")

    # Add error information
    if "error" in event_dict:
        context_parts.append(f"error={event_dict.pop('error')}")

    # Add remaining important fields (limit to avoid clutter)
    remaining_important = {}
    for key, value in event_dict.items():
        if key in ["workflow_id", "execution_id", "task_id", "next_run_at"]:
            remaining_important[key] = value
        elif len(remaining_important) < 3:  # Limit additional fields
            remaining_important[key] = value

    if remaining_important:
        for key, value in remaining_important.items():
            if isinstance(value, str) and len(value) > 20:
                value = value[:17] + "..."
            context_parts.append(f"{key}={value}")

    # Combine main line with context
    if context_parts:
        context_str = " | ".join(context_parts)
        return f"{main_line} | {context_str}"
    else:
        return main_line


def setup_logging(log_level: str = "INFO", log_format: str = "json") -> None:
    """
    Configure structured logging for the application.

    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_format: Logging format ("json" or "text")
    """
    global _logging_configured

    # Only configure once unless forced
    if _logging_configured:
        return
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, log_level.upper()),
        force=True,  # Force reconfiguration
    )

    # Set root logger level to ensure all loggers respect the global level
    logging.getLogger().setLevel(getattr(logging, log_level.upper()))

    # Silence noisy third-party loggers
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("celery").setLevel(logging.WARNING)
    logging.getLogger("redis").setLevel(logging.WARNING)

    # Configure processors based on format
    processors: list[Processor] = [
        structlog.contextvars.merge_contextvars,
        add_correlation_id,
        add_service_info,
        filter_sensitive_data,
        structlog.processors.add_log_level,
    ]

    if log_format == "json":
        # JSON format for production
        processors.extend(
            [
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.JSONRenderer(),
            ]
        )
    else:
        # Clean, minimal format for development
        processors.extend(
            [
                clean_console_renderer,
            ]
        )

    # Configure structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.make_filtering_bound_logger(
            getattr(logging, log_level.upper())
        ),
        logger_factory=structlog.PrintLoggerFactory(),
        cache_logger_on_first_use=True,
    )

    # Mark as configured
    _logging_configured = True


def get_logger(name: str) -> structlog.BoundLogger:
    """
    Get a structured logger instance.

    Args:
        name: Logger name (typically __name__)

    Returns:
        structlog.BoundLogger: Configured logger instance
    """
    return structlog.get_logger(name)


def set_correlation_id(correlation_id: Optional[str] = None) -> str:
    """
    Set correlation ID for the current context.

    Args:
        correlation_id: Correlation ID to set, or None to generate a new one

    Returns:
        str: The correlation ID that was set
    """
    if correlation_id is None:
        correlation_id = str(uuid.uuid4())

    correlation_id_var.set(correlation_id)
    return correlation_id


def get_correlation_id() -> Optional[str]:
    """
    Get the current correlation ID.

    Returns:
        Optional[str]: Current correlation ID, or None if not set
    """
    return correlation_id_var.get()


def clear_correlation_id() -> None:
    """Clear the current correlation ID."""
    correlation_id_var.set(None)


class LoggerMixin:
    """
    Mixin class to add logging capabilities to other classes.

    This mixin provides a logger property that automatically includes
    the class name in log events.
    """

    @property
    def logger(self) -> structlog.BoundLogger:
        """Get a logger bound to this class."""
        return get_logger(self.__class__.__name__)
