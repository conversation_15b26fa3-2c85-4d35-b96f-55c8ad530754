"""
Database utilities for the Scheduler Service.

This module provides database-related utilities including transaction
management, retry logic, and connection helpers.
"""

import asyncio
import functools
from contextlib import asynccontextmanager
from typing import Any, Callable, Optional, AsyncGenerator

from sqlalchemy.exc import SQLAlchemyError, DisconnectionError, TimeoutError
from sqlalchemy.ext.asyncio import AsyncSession

from src.utils.logger import get_logger
from src.utils.retry import retry_async

logger = get_logger(__name__)


# Database-specific exceptions that should trigger retries
DB_RETRY_EXCEPTIONS = (
    DisconnectionError,
    TimeoutError,
    ConnectionError,
)


def with_db_retry(
    max_retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 30.0,
):
    """
    Decorator for database operations with retry logic.

    Args:
        max_retries: Maximum number of retry attempts
        base_delay: Base delay in seconds
        max_delay: Maximum delay in seconds

    Returns:
        Decorated function with database retry logic
    """
    return retry_async(
        max_retries=max_retries,
        base_delay=base_delay,
        max_delay=max_delay,
        exceptions=DB_RETRY_EXCEPTIONS,
    )


class TransactionManager:
    """
    Context manager for database transactions with automatic rollback.

    Provides safe transaction handling with automatic rollback on exceptions
    and proper resource cleanup.
    """

    def __init__(self, session: AsyncSession):
        """
        Initialize the transaction manager.

        Args:
            session: Database session to manage
        """
        self.session = session
        self._in_transaction = False

    async def __aenter__(self):
        """Enter the transaction context."""
        if not self._in_transaction:
            await self.session.begin()
            self._in_transaction = True
            logger.debug("Started database transaction")
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit the transaction context."""
        if self._in_transaction:
            try:
                if exc_type is None:
                    await self.session.commit()
                    logger.debug("Committed database transaction")
                else:
                    await self.session.rollback()
                    logger.debug(
                        f"Rolled back database transaction due to {exc_type.__name__}"
                    )
            except Exception as e:
                logger.error(f"Error during transaction cleanup: {e}")
                try:
                    await self.session.rollback()
                except Exception as rollback_error:
                    logger.error(f"Error during rollback: {rollback_error}")
            finally:
                self._in_transaction = False

    async def commit(self):
        """Manually commit the transaction."""
        if self._in_transaction:
            await self.session.commit()
            logger.debug("Manually committed database transaction")

    async def rollback(self):
        """Manually rollback the transaction."""
        if self._in_transaction:
            await self.session.rollback()
            logger.debug("Manually rolled back database transaction")


@asynccontextmanager
async def transaction_scope(
    session: AsyncSession,
) -> AsyncGenerator[TransactionManager, None]:
    """
    Context manager for database transaction scope.

    Args:
        session: Database session

    Yields:
        TransactionManager: Transaction manager instance
    """
    transaction_manager = TransactionManager(session)
    async with transaction_manager:
        yield transaction_manager


async def execute_with_retry(
    session: AsyncSession, operation: Callable, *args, max_retries: int = 3, **kwargs
) -> Any:
    """
    Execute a database operation with retry logic.

    Args:
        session: Database session
        operation: Operation to execute
        *args: Positional arguments for the operation
        max_retries: Maximum number of retry attempts
        **kwargs: Keyword arguments for the operation

    Returns:
        Operation result

    Raises:
        Exception: Last exception if all retries fail
    """

    @with_db_retry(max_retries=max_retries)
    async def _execute():
        return await operation(session, *args, **kwargs)

    return await _execute()


async def safe_execute(
    session: AsyncSession,
    operation: Callable,
    *args,
    default_value: Any = None,
    log_errors: bool = True,
    **kwargs,
) -> Any:
    """
    Safely execute a database operation with error handling.

    Args:
        session: Database session
        operation: Operation to execute
        *args: Positional arguments for the operation
        default_value: Value to return on error
        log_errors: Whether to log errors
        **kwargs: Keyword arguments for the operation

    Returns:
        Operation result or default_value on error
    """
    try:
        return await operation(session, *args, **kwargs)
    except Exception as e:
        if log_errors:
            logger.error(f"Database operation failed: {e}", exc_info=True)
        return default_value


class DatabaseHealthChecker:
    """
    Database health checker for monitoring connection status.
    """

    def __init__(self, session: AsyncSession):
        """
        Initialize the health checker.

        Args:
            session: Database session to check
        """
        self.session = session

    async def check_connection(self) -> bool:
        """
        Check if the database connection is healthy.

        Returns:
            bool: True if connection is healthy
        """
        try:
            # Execute a simple query to test the connection
            result = await self.session.execute("SELECT 1")
            result.scalar()
            return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False

    async def check_tables_exist(self, table_names: list) -> bool:
        """
        Check if required tables exist in the database.

        Args:
            table_names: List of table names to check

        Returns:
            bool: True if all tables exist
        """
        try:
            for table_name in table_names:
                result = await self.session.execute(
                    f"SELECT 1 FROM information_schema.tables WHERE table_name = '{table_name}'"
                )
                if not result.scalar():
                    logger.error(f"Table {table_name} does not exist")
                    return False
            return True
        except Exception as e:
            logger.error(f"Table existence check failed: {e}")
            return False


def handle_db_errors(
    default_value: Any = None,
    log_errors: bool = True,
    reraise: bool = False,
):
    """
    Decorator for handling database errors.

    Args:
        default_value: Value to return on error
        log_errors: Whether to log errors
        reraise: Whether to re-raise the exception after logging

    Returns:
        Decorated function with error handling
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            try:
                return await func(*args, **kwargs)
            except SQLAlchemyError as e:
                if log_errors:
                    logger.error(
                        f"Database error in {func.__name__}: {e}", exc_info=True
                    )
                if reraise:
                    raise
                return default_value
            except Exception as e:
                if log_errors:
                    logger.error(
                        f"Unexpected error in {func.__name__}: {e}", exc_info=True
                    )
                if reraise:
                    raise
                return default_value

        return wrapper

    return decorator


async def ensure_connection(session: AsyncSession) -> bool:
    """
    Ensure the database connection is active.

    Args:
        session: Database session to check

    Returns:
        bool: True if connection is active
    """
    try:
        # Test the connection with a simple query
        await session.execute("SELECT 1")
        return True
    except Exception as e:
        logger.warning(f"Database connection test failed: {e}")
        try:
            # Try to reconnect
            await session.rollback()
            await session.execute("SELECT 1")
            logger.info("Database connection restored")
            return True
        except Exception as reconnect_error:
            logger.error(f"Failed to restore database connection: {reconnect_error}")
            return False
