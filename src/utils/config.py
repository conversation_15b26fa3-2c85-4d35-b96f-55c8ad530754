"""
Configuration management for the Scheduler Service.

This module provides centralized configuration management using Pydantic
BaseSettings for environment variable validation and type conversion.
"""

import os
from typing import Optional

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """
    Application settings loaded from environment variables.

    This class uses Pydantic BaseSettings to automatically load and validate
    configuration from environment variables with proper type conversion.
    """

    # Application Configuration
    debug: bool = Field(default=False, alias="DEBUG", description="Enable debug mode")
    host: str = Field(
        default="0.0.0.0", alias="HOST", description="Host to bind the server"
    )
    port: int = Field(default=8000, alias="PORT", description="Port to bind the server")
    log_level: str = Field(
        default="INFO", alias="LOG_LEVEL", description="Logging level"
    )
    log_format: str = Field(
        default="json", alias="LOG_FORMAT", description="Logging format (json or text)"
    )

    # Database Configuration
    database_url: str = Field(
        default="postgresql+asyncpg://scheduler_user:scheduler_password@localhost:5432/scheduler_db",
        alias="DATABASE_URL",
        description="PostgreSQL database URL",
    )

    # Database Pool Configuration
    db_pool_size: int = Field(
        default=20,
        alias="DB_POOL_SIZE",
        description="Database connection pool size",
    )
    db_max_overflow: int = Field(
        default=10,
        alias="DB_MAX_OVERFLOW",
        description="Database connection pool max overflow",
    )
    db_pool_timeout: int = Field(
        default=30,
        alias="DB_POOL_TIMEOUT",
        description="Database connection pool timeout",
    )
    db_pool_recycle: int = Field(
        default=3600,
        alias="DB_POOL_RECYCLE",
        description="Database connection pool recycle time",
    )
    db_echo: bool = Field(
        default=False,
        alias="DB_ECHO",
        description="Enable SQLAlchemy query logging",
    )

    # Workflow Service Configuration
    workflow_service_url: str = Field(
        default="http://localhost:8001",
        alias="WORKFLOW_SERVICE_URL",
        description="URL of the workflow service",
    )
    workflow_service_api_key: str = Field(
        default="your-workflow-service-api-key",
        alias="WORKFLOW_SERVICE_API_KEY",
        description="API key for workflow service",
    )
    workflow_service_timeout: int = Field(
        default=60,
        alias="WORKFLOW_SERVICE_TIMEOUT",
        description="Workflow service timeout in seconds",
    )

    # Auth Service Configuration
    auth_service_url: str = Field(
        default="http://localhost:8002",
        alias="AUTH_SERVICE_URL",
        description="URL of the authentication service",
    )
    auth_service_timeout: int = Field(
        default=30,
        alias="AUTH_SERVICE_TIMEOUT",
        description="Auth service timeout in seconds",
    )

    # Redis Configuration
    redis_url: str = Field(
        default="redis://localhost:6379/0",
        alias="REDIS_URL",
        description="Redis connection URL",
    )

    # Task Queue Configuration
    task_queue_redis_url: str = Field(
        default="redis://localhost:6379/0",
        alias="TASK_QUEUE_REDIS_URL",
        description="Redis URL for task queue",
    )
    task_queue_default_timeout: int = Field(
        default=30,
        alias="TASK_QUEUE_DEFAULT_TIMEOUT",
        description="Default timeout for task queue operations",
    )
    task_queue_max_retries: int = Field(
        default=3,
        alias="TASK_QUEUE_MAX_RETRIES",
        description="Maximum retries for task queue operations",
    )
    task_queue_retry_delay: int = Field(
        default=5,
        alias="TASK_QUEUE_RETRY_DELAY",
        description="Retry delay for task queue operations",
    )

    # Distributed Locking Configuration
    distributed_lock_redis_url: str = Field(
        default="redis://localhost:6379/0",
        alias="DISTRIBUTED_LOCK_REDIS_URL",
        description="Redis URL for distributed locking",
    )
    distributed_lock_default_ttl: int = Field(
        default=300,
        alias="DISTRIBUTED_LOCK_DEFAULT_TTL",
        description="Default TTL for distributed locks",
    )
    distributed_lock_retry_delay: float = Field(
        default=0.1,
        alias="DISTRIBUTED_LOCK_RETRY_DELAY",
        description="Retry delay for distributed lock acquisition",
    )
    distributed_lock_max_retries: int = Field(
        default=10,
        alias="DISTRIBUTED_LOCK_MAX_RETRIES",
        description="Maximum retries for distributed lock acquisition",
    )

    # Instance Configuration
    hostname: str = Field(
        default="localhost",
        alias="HOSTNAME",
        description="Hostname for this instance",
    )
    instance_id: Optional[str] = Field(
        default=None,
        alias="INSTANCE_ID",
        description="Unique instance identifier",
    )
    version: str = Field(
        default="0.1.0",
        alias="VERSION",
        description="Application version",
    )

    # Security
    secret_key: str = Field(
        default="your-secret-key-change-in-production",
        alias="SECRET_KEY",
        description="Secret key for cryptographic operations",
    )
    api_key: str = Field(
        default="your-api-key-change-in-production",
        alias="API_KEY",
        description="API key for internal service authentication",
    )

    # HTTP Configuration
    http_timeout: int = Field(
        default=30, alias="HTTP_TIMEOUT", description="Default HTTP timeout in seconds"
    )

    # Retry Configuration
    max_retry_attempts: int = Field(
        default=5,
        alias="MAX_RETRY_ATTEMPTS",
        description="Maximum number of retry attempts",
    )
    retry_backoff_factor: float = Field(
        default=2.0,
        alias="RETRY_BACKOFF_FACTOR",
        description="Exponential backoff factor",
    )
    retry_max_delay: int = Field(
        default=300,
        alias="RETRY_MAX_DELAY",
        description="Maximum retry delay in seconds",
    )

    # Scheduler Configuration
    scheduler_batch_size: int = Field(
        default=50,
        alias="SCHEDULER_BATCH_SIZE",
        description="Batch size for scheduler processing",
    )
    scheduler_concurrency: int = Field(
        default=10,
        alias="SCHEDULER_CONCURRENCY",
        description="Concurrency level for scheduler processing",
    )
    scheduler_cycle_interval: int = Field(
        default=30,
        alias="SCHEDULER_CYCLE_INTERVAL",
        description="Scheduler cycle interval in seconds",
    )

    # Production Scalability Configuration
    enable_concurrent_processing: bool = Field(
        default=True,
        alias="ENABLE_CONCURRENT_PROCESSING",
        description="Enable concurrent processing",
    )
    max_concurrent_schedulers: int = Field(
        default=100,
        alias="MAX_CONCURRENT_SCHEDULERS",
        description="Maximum concurrent schedulers",
    )
    enable_task_queue: bool = Field(
        default=True,
        alias="ENABLE_TASK_QUEUE",
        description="Enable Redis task queue",
    )
    enable_distributed_locking: bool = Field(
        default=True,
        alias="ENABLE_DISTRIBUTED_LOCKING",
        description="Enable distributed locking",
    )

    # Task Worker Configuration
    task_worker_concurrency: int = Field(
        default=5,
        alias="TASK_WORKER_CONCURRENCY",
        description="Task worker concurrency level",
    )
    task_worker_queues: str = Field(
        default="workflow_execution,scheduler_tasks",
        alias="TASK_WORKER_QUEUES",
        description="Comma-separated list of task worker queues",
    )
    task_worker_timeout: int = Field(
        default=300,
        alias="TASK_WORKER_TIMEOUT",
        description="Task worker timeout in seconds",
    )

    # Monitoring and Metrics
    enable_metrics: bool = Field(
        default=True, alias="ENABLE_METRICS", description="Enable Prometheus metrics"
    )
    metrics_port: int = Field(
        default=9090, alias="METRICS_PORT", description="Port for metrics endpoint"
    )
    enable_scheduler_metrics: bool = Field(
        default=True,
        alias="ENABLE_SCHEDULER_METRICS",
        description="Enable scheduler metrics collection",
    )
    enable_task_queue_metrics: bool = Field(
        default=True,
        alias="ENABLE_TASK_QUEUE_METRICS",
        description="Enable task queue metrics collection",
    )

    # Health Check Configuration
    health_check_redis: bool = Field(
        default=True,
        alias="HEALTH_CHECK_REDIS",
        description="Include Redis in health checks",
    )
    health_check_database: bool = Field(
        default=True,
        alias="HEALTH_CHECK_DATABASE",
        description="Include database in health checks",
    )
    health_check_timeout: int = Field(
        default=10,
        alias="HEALTH_CHECK_TIMEOUT",
        description="Health check timeout in seconds",
    )

    # Production Mode Settings
    production_mode: bool = Field(
        default=False,
        alias="PRODUCTION_MODE",
        description="Enable production mode optimizations",
    )

    @property
    def database_url_sync(self) -> str:
        """Get synchronous database URL."""
        return self.database_url.replace("postgresql://", "postgresql://").replace(
            "postgresql+asyncpg://", "postgresql://"
        )

    @field_validator("retry_backoff_factor")
    @classmethod
    def validate_retry_backoff_factor(cls, v: float) -> float:
        """Validate retry backoff factor is positive."""
        if v <= 0:
            raise ValueError("Retry backoff factor must be positive")
        return v

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
        "validate_assignment": True,
        "extra": "ignore",
    }


def get_settings() -> Settings:
    """
    Get application settings.

    Returns:
        Settings: Application settings instance
    """
    return Settings()


def get_database_url() -> str:
    """
    Get database URL for SQLAlchemy.

    Returns:
        str: Database connection URL
    """
    return get_settings().database_url


def is_development() -> bool:
    """
    Check if the application is running in development mode.

    Returns:
        bool: True if in development mode
    """
    return get_settings().debug


def is_production() -> bool:
    """
    Check if the application is running in production mode.

    Returns:
        bool: True if in production mode
    """
    return not get_settings().debug
