"""
Database initialization utilities for the Scheduler Service.

This module provides utilities to automatically create databases and tables
if they don't exist during service startup.
"""

import asyncio
from urllib.parse import urlparse, urlunparse

import asyncpg
from sqlalchemy.ext.asyncio import create_async_engine

from src.database.connection import Base
from src.utils.config import get_settings
from src.utils.logger import get_logger

logger = get_logger(__name__)


async def create_database_if_not_exists():
    """
    Create the database if it doesn't exist.

    This function connects to PostgreSQL using the 'postgres' database
    and creates the target database if it doesn't exist.
    """
    settings = get_settings()

    # Parse the database URL
    parsed_url = urlparse(settings.database_url)

    # Extract database name
    database_name = parsed_url.path.lstrip("/")

    # Create connection URL to postgres database (for creating the target database)
    postgres_url_parts = list(parsed_url)
    postgres_url_parts[2] = "/postgres"  # Change path to postgres database
    postgres_url = urlunparse(postgres_url_parts)

    # Remove the +asyncpg part for direct asyncpg connection
    postgres_url_clean = postgres_url.replace("postgresql+asyncpg://", "postgresql://")

    logger.info(f"Checking if database '{database_name}' exists...")

    try:
        # Connect to postgres database
        conn = await asyncpg.connect(postgres_url_clean)

        try:
            # Check if database exists
            result = await conn.fetchval(
                "SELECT 1 FROM pg_database WHERE datname = $1", database_name
            )

            if result:
                logger.info(f"Database '{database_name}' already exists")
            else:
                logger.info(f"Creating database '{database_name}'...")

                # Create the database
                await conn.execute(f'CREATE DATABASE "{database_name}"')
                logger.info(f"Successfully created database '{database_name}'")

        finally:
            await conn.close()

    except Exception as e:
        logger.error(f"Error creating database '{database_name}': {e}")
        raise


async def create_tables_if_not_exist():
    """
    Create all tables if they don't exist.

    This function connects to the target database and creates all tables
    defined in the SQLAlchemy models.
    """
    settings = get_settings()

    logger.info("Creating database tables if they don't exist...")

    try:
        # Create async engine for the target database
        engine = create_async_engine(
            settings.database_url,
            echo=settings.db_echo,
        )

        try:
            # Create all tables
            async with engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)

            logger.info("Successfully created/verified all database tables")

        finally:
            await engine.dispose()

    except Exception as e:
        logger.error(f"Error creating tables: {e}")
        raise


async def initialize_database():
    """
    Initialize the database by creating the database and tables if they don't exist.

    This is the main function to call during service startup.
    """
    logger.info("Starting database initialization...")

    try:
        # Step 1: Create database if it doesn't exist
        await create_database_if_not_exists()

        # Step 2: Create tables if they don't exist
        await create_tables_if_not_exist()

        logger.info("Database initialization completed successfully")

    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise


async def check_database_connection():
    """
    Check if we can connect to the database.

    Returns:
        bool: True if connection is successful, False otherwise
    """
    settings = get_settings()

    try:
        engine = create_async_engine(settings.database_url)

        async with engine.begin() as conn:
            await conn.execute("SELECT 1")

        await engine.dispose()
        logger.info("Database connection test successful")
        return True

    except Exception as e:
        logger.error(f"Database connection test failed: {e}")
        return False


def run_database_init():
    """
    Synchronous wrapper for database initialization.

    This can be called from CLI or other synchronous contexts.
    """
    asyncio.run(initialize_database())
