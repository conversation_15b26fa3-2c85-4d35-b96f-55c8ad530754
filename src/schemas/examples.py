"""
Example schemas for OpenAPI documentation.

This module provides example request and response data for the Swagger documentation.
"""

from src.schemas.scheduler import (
    SimplifiedSchedulerCreate,
    SimplifiedSchedulerResponse,
    SimplifiedSchedulerUpdate,
    ScheduleFrequency,
)

# Example scheduler creation requests
DAILY_SCHEDULER_EXAMPLE = {
    "name": "Daily Report Generator",
    "frequency": "daily",
    "time": "09:00",
    "timezone": "America/New_York",
    "is_active": True,
    "workflow_id": "report-workflow-123",
    "scheduler_metadata": {"department": "finance", "priority": "high"},
    "input_values": [
        {
            "field_name": "report_type",
            "field_value": "daily_summary",
            "field_type": "string",
        }
    ],
}

WEEKLY_SCHEDULER_EXAMPLE = {
    "name": "Weekly Team Meeting Reminder",
    "frequency": "weekly",
    "time": "14:00",
    "days_of_week": ["Monday", "Wednesday", "Friday"],
    "timezone": "UTC",
    "is_active": True,
    "workflow_id": "meeting-reminder-workflow",
    "scheduler_metadata": {"team": "engineering", "meeting_type": "standup"},
}

MONTHLY_SCHEDULER_EXAMPLE = {
    "name": "Monthly Invoice Processing",
    "frequency": "monthly",
    "time": "08:00",
    "days_of_month": [1, 15],
    "timezone": "Europe/London",
    "is_active": True,
    "workflow_id": "invoice-processing-workflow",
    "scheduler_metadata": {"department": "accounting", "process_type": "automated"},
}

CUSTOM_SCHEDULER_EXAMPLE = {
    "name": "Weekday Morning Backup",
    "frequency": "custom",
    "cron_expression": "0 6 * * 1-5",
    "timezone": "Asia/Tokyo",
    "is_active": True,
    "workflow_id": "backup-workflow",
    "scheduler_metadata": {"backup_type": "incremental", "retention_days": 30},
}

HOURLY_SCHEDULER_EXAMPLE = {
    "name": "System Health Check",
    "frequency": "hourly",
    "timezone": "UTC",
    "is_active": True,
    "workflow_id": "health-check-workflow",
    "scheduler_metadata": {
        "check_type": "comprehensive",
        "alert_threshold": "critical",
    },
}

EVERY_MINUTE_SCHEDULER_EXAMPLE = {
    "name": "Real-time Data Sync",
    "frequency": "every_minute",
    "timezone": "UTC",
    "is_active": True,
    "workflow_id": "data-sync-workflow",
    "scheduler_metadata": {"sync_type": "incremental", "source": "external_api"},
}

# Example scheduler responses
SCHEDULER_RESPONSE_EXAMPLE = {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "name": "Daily Report Generator",
    "frequency": "daily",
    "time": "09:00",
    "timezone": "America/New_York",
    "is_active": True,
    "workflow_id": "report-workflow-123",
    "user_id": "user-123",
    "created_at": "2025-01-17T10:30:00Z",
    "updated_at": "2025-01-17T10:30:00Z",
    "last_run_at": "2025-01-17T14:00:00Z",
    "next_run_at": "2025-01-18T14:00:00Z",
    "scheduler_metadata": {"department": "finance", "priority": "high"},
    "input_values": [
        {
            "field_name": "report_type",
            "field_value": "daily_summary",
            "field_type": "string",
        }
    ],
}

# Example update requests
SCHEDULER_UPDATE_EXAMPLE = {
    "name": "Updated Daily Report Generator",
    "time": "10:00",
    "is_active": False,
    "scheduler_metadata": {"department": "finance", "priority": "medium"},
}

# Example error responses
ERROR_RESPONSE_EXAMPLES = {
    "validation_error": {
        "detail": "Invalid schedule configuration: time is required for daily frequency"
    },
    "not_found": {"detail": "Scheduler 550e8400-e29b-41d4-a716-446655440000 not found"},
    "unauthorized": {
        "detail": "Authentication required. Provide either Bearer token or X-API-Key header."
    },
    "forbidden": {"detail": "Access denied. You can only access your own schedulers."},
    "internal_error": {"detail": "Internal server error"},
}

# OpenAPI examples for different scheduler types
SCHEDULER_CREATE_EXAMPLES = {
    "daily_scheduler": {
        "summary": "Daily Scheduler",
        "description": "A scheduler that runs daily at a specific time",
        "value": DAILY_SCHEDULER_EXAMPLE,
    },
    "weekly_scheduler": {
        "summary": "Weekly Scheduler",
        "description": "A scheduler that runs on specific days of the week",
        "value": WEEKLY_SCHEDULER_EXAMPLE,
    },
    "monthly_scheduler": {
        "summary": "Monthly Scheduler",
        "description": "A scheduler that runs on specific days of the month",
        "value": MONTHLY_SCHEDULER_EXAMPLE,
    },
    "custom_scheduler": {
        "summary": "Custom Cron Scheduler",
        "description": "A scheduler with custom cron expression",
        "value": CUSTOM_SCHEDULER_EXAMPLE,
    },
    "hourly_scheduler": {
        "summary": "Hourly Scheduler",
        "description": "A scheduler that runs every hour",
        "value": HOURLY_SCHEDULER_EXAMPLE,
    },
    "every_minute_scheduler": {
        "summary": "Every Minute Scheduler",
        "description": "A scheduler that runs every minute",
        "value": EVERY_MINUTE_SCHEDULER_EXAMPLE,
    },
}

SCHEDULER_UPDATE_EXAMPLES = {
    "partial_update": {
        "summary": "Partial Update",
        "description": "Update only specific fields of a scheduler",
        "value": SCHEDULER_UPDATE_EXAMPLE,
    },
    "deactivate": {
        "summary": "Deactivate Scheduler",
        "description": "Deactivate a scheduler",
        "value": {"is_active": False},
    },
    "change_time": {
        "summary": "Change Schedule Time",
        "description": "Change the execution time of a daily scheduler",
        "value": {"time": "15:30"},
    },
    "change_frequency": {
        "summary": "Change Frequency",
        "description": "Change from daily to weekly schedule",
        "value": {
            "frequency": "weekly",
            "days_of_week": ["Monday", "Wednesday", "Friday"],
            "time": "10:00",
        },
    },
}
