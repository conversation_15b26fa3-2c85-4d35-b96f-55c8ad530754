"""
Authentication middleware for the Scheduler Service.

This module provides authentication functionality including bearer token
validation and user extraction from requests.
"""

from typing import Dict, Any, Optional

from fastapi import HTT<PERSON>Exception, Request, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from src.utils.auth_client import validate_bearer_token
from src.utils.config import get_settings
from src.utils.logger import get_logger

logger = get_logger(__name__)

# Security scheme for OpenAPI documentation
bearer_scheme = HTTPBearer(auto_error=False)


async def get_current_user(request: Request) -> Dict[str, Any]:
    """
    Extract and validate the current user from bearer token.

    This function validates Bearer token authentication with the external auth service.

    Args:
        request: FastAPI request object

    Returns:
        Dict containing user information

    Raises:
        HTTPException: If authentication fails
    """
    # Check for Authorization header
    authorization = request.headers.get("Authorization")
    if authorization:
        return await _validate_bearer_token(authorization)

    # No authentication provided
    logger.warning("No authentication provided in request")
    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Authentication required. Provide Bearer token in Authorization header.",
        headers={"WWW-Authenticate": "Bearer"},
    )


async def _validate_bearer_token(authorization: str) -> Dict[str, Any]:
    """
    Validate Bearer token authentication.

    Args:
        authorization: Authorization header value

    Returns:
        Dict containing user information from auth service

    Raises:
        HTTPException: If bearer token is invalid
    """
    # Check if it's a Bearer token
    if not authorization.startswith("Bearer "):
        logger.warning(
            "Invalid authorization scheme", authorization_header=authorization[:20]
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authorization scheme. Use 'Bearer <token>'",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Extract token
    token = authorization[7:]  # Remove "Bearer " prefix
    if not token:
        logger.warning("Empty bearer token provided")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Bearer token is required",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Validate token with auth service
    try:
        user_data = await validate_bearer_token(token)

        if not user_data:
            logger.warning("Invalid bearer token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired token",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Ensure we have required user fields
        if not user_data.get("id"):
            logger.error("User data missing required 'id' field", user_data=user_data)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Invalid user data from authentication service",
            )

        # Add auth method to user data
        user_data["auth_method"] = "bearer_token"

        logger.debug(
            "Successfully authenticated user with bearer token",
            user_id=user_data.get("id"),
            email=user_data.get("email"),
        )

        return user_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error validating bearer token", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication service error",
        )


async def get_current_user_optional(request: Request) -> Optional[Dict[str, Any]]:
    """
    Extract and validate the current user from the bearer token, but don't require authentication.

    Args:
        request: FastAPI request object

    Returns:
        Dict containing user information if authenticated, None otherwise
    """
    try:
        return await get_current_user(request)
    except HTTPException:
        # Return None if authentication fails instead of raising an exception
        return None
    except Exception as e:
        logger.warning("Error during optional authentication", error=str(e))
        return None


async def extract_user_id_from_request(request: Request) -> str:
    """
    Extract user ID from authenticated request.

    Args:
        request: FastAPI request object

    Returns:
        str: User ID

    Raises:
        HTTPException: If authentication fails or user ID not found
    """
    user_data = await get_current_user(request)
    user_id = user_data.get("id")

    if not user_id:
        logger.error(
            "User ID not found in authenticated user data", user_data=user_data
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="User ID not available",
        )

    return user_id


def create_auth_dependency():
    """
    Create an authentication dependency that can be used with FastAPI Depends().

    Returns:
        Callable that validates authentication and returns user data
    """

    async def auth_dependency(request: Request) -> Dict[str, Any]:
        return await get_current_user(request)

    return auth_dependency


# Create the default auth dependency
get_authenticated_user = create_auth_dependency()
