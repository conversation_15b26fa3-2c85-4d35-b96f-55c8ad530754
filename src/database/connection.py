"""
Database connection management for the Scheduler Service.

This module provides database connection setup, session management,
and connection pooling for PostgreSQL using SQLAlchemy.
"""

from contextlib import asynccontextmanager
from typing import AsyncGenerator

from sqlalchemy import create_engine, event, text
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import DeclarativeBase, sessionmaker
from sqlalchemy.pool import Queue<PERSON><PERSON>, AsyncAdaptedQueuePool

from src.utils.config import get_settings
from src.utils.logger import get_logger

logger = get_logger(__name__)


class Base(DeclarativeBase):
    """Base class for all database models."""

    pass


class DatabaseManager:
    """
    Database connection and session manager.

    This class handles database connection setup, session management,
    and provides both sync and async database operations.
    """

    def __init__(self):
        """Initialize the database manager."""
        self.settings = get_settings()
        self._engine = None
        self._async_engine = None
        self._session_factory = None
        self._async_session_factory = None

    def get_engine(self):
        """
        Get or create the synchronous database engine.

        Returns:
            Engine: SQLAlchemy engine instance
        """
        if self._engine is None:
            self._engine = create_engine(
                self.settings.database_url_sync,
                poolclass=QueuePool,
                pool_size=self.settings.db_pool_size,
                max_overflow=self.settings.db_max_overflow,
                pool_timeout=self.settings.db_pool_timeout,
                pool_recycle=self.settings.db_pool_recycle,
                echo=self.settings.db_echo,
            )
            logger.info("Synchronous database engine created")
        return self._engine

    def get_async_engine(self):
        """
        Get or create the asynchronous database engine.

        Returns:
            AsyncEngine: SQLAlchemy async engine instance
        """
        if self._async_engine is None:
            self._async_engine = create_async_engine(
                self.settings.database_url,
                poolclass=AsyncAdaptedQueuePool,
                pool_size=self.settings.db_pool_size,
                max_overflow=self.settings.db_max_overflow,
                pool_timeout=self.settings.db_pool_timeout,
                pool_recycle=self.settings.db_pool_recycle,
                echo=self.settings.db_echo,
            )
            logger.info("Asynchronous database engine created")
        return self._async_engine

    def get_session_factory(self):
        """
        Get or create the synchronous session factory.

        Returns:
            sessionmaker: SQLAlchemy session factory
        """
        if self._session_factory is None:
            self._session_factory = sessionmaker(
                bind=self.get_engine(),
                autocommit=False,
                autoflush=False,
            )
            logger.info("Synchronous session factory created")
        return self._session_factory

    def get_async_session_factory(self):
        """
        Get or create the asynchronous session factory.

        Returns:
            async_sessionmaker: SQLAlchemy async session factory
        """
        if self._async_session_factory is None:
            self._async_session_factory = async_sessionmaker(
                bind=self.get_async_engine(),
                class_=AsyncSession,
                autocommit=False,
                autoflush=False,
                expire_on_commit=False,
            )
            logger.info("Asynchronous session factory created")
        return self._async_session_factory

    @asynccontextmanager
    async def get_async_session(self) -> AsyncGenerator[AsyncSession, None]:
        """
        Get an async database session with automatic cleanup.

        Yields:
            AsyncSession: Database session
        """
        session_factory = self.get_async_session_factory()
        async with session_factory() as session:
            try:
                yield session
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()

    async def close_async_engine(self):
        """Close the async database engine."""
        if self._async_engine:
            await self._async_engine.dispose()
            logger.info("Async database engine closed")

    def close_engine(self):
        """Close the synchronous database engine."""
        if self._engine:
            self._engine.dispose()
            logger.info("Synchronous database engine closed")

    async def test_connection(self) -> bool:
        """
        Test the database connection.

        Returns:
            bool: True if connection is successful, False otherwise
        """
        try:
            async with self.get_async_session() as session:
                result = await session.execute(text("SELECT 1"))
                result.scalar()
                logger.info("Database connection test successful")
                return True
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False


# Global database manager instance
_db_manager = None


def get_database_manager() -> DatabaseManager:
    """
    Get the global database manager instance.

    Returns:
        DatabaseManager: Database manager instance
    """
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
    return _db_manager


async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency function to get an async database session.

    Yields:
        AsyncSession: Database session
    """
    db_manager = get_database_manager()
    async with db_manager.get_async_session() as session:
        yield session


def get_session():
    """
    Dependency function to get a synchronous database session.

    Yields:
        Session: Database session
    """
    db_manager = get_database_manager()
    session_factory = db_manager.get_session_factory()
    session = session_factory()
    try:
        yield session
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()


async def init_database():
    """Initialize database tables."""
    db_manager = get_database_manager()
    async_engine = db_manager.get_async_engine()

    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    logger.info("Database tables initialized")


async def close_database():
    """Close database connections."""
    db_manager = get_database_manager()
    await db_manager.close_async_engine()
    db_manager.close_engine()
    logger.info("Database connections closed")
