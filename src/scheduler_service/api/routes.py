"""
API routes for the Scheduler Service.

This module defines FastAPI routes for managing schedulers and their executions.
"""

from typing import List, Optional, Dict, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status, Request, Body
from sqlalchemy.ext.asyncio import AsyncSession

from src.api.middleware.auth import (
    get_authenticated_user,
    extract_user_id_from_request,
)
from src.core.scheduler_manager import SchedulerManager
from src.database.connection import get_async_session
from src.schemas.scheduler import (
    SimplifiedSchedulerCreate,
    SimplifiedSchedulerUpdate,
    SimplifiedSchedulerResponse,
)
from src.schemas.examples import (
    SCHEDULER_CREATE_EXAMPLES,
    SCHEDULER_UPDATE_EXAMPLES,
)
from src.utils.logger import get_logger

logger = get_logger(__name__)

# Create router with tags and metadata
router = APIRouter(
    tags=["schedulers"],
    responses={
        400: {"description": "Bad Request - Invalid input data"},
        401: {"description": "Unauthorized - Authentication required"},
        404: {"description": "Not Found - Resource not found"},
        500: {"description": "Internal Server Error"},
    },
)


@router.post(
    "/schedulers",
    response_model=SimplifiedSchedulerResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new scheduler",
    description="""
    Create a new scheduler with the specified configuration.

    This endpoint allows you to create a new scheduler that will automatically
    execute workflows at specified intervals. The scheduler supports various
    frequency types including daily, weekly, monthly, and custom cron expressions.

    **Authentication**: Requires Bearer token in Authorization header.

    **Schedule Types**:
    - `every_minute`: Executes every minute
    - `hourly`: Executes every hour
    - `daily`: Executes daily at specified time
    - `weekly`: Executes on specified days of the week
    - `monthly`: Executes on specified days of the month
    - `custom`: Uses cron expression for complex scheduling

    **Examples**:
    - Daily at 9:00 AM: `{"frequency": "daily", "time": "09:00"}`
    - Weekly on Mon/Wed/Fri: `{"frequency": "weekly", "time": "14:00", "days_of_week": ["Monday", "Wednesday", "Friday"]}`
    - Monthly on 1st and 15th: `{"frequency": "monthly", "time": "08:00", "days_of_month": [1, 15]}`
    - Custom cron: `{"frequency": "custom", "cron_expression": "0 9 * * 1-5"}`
    """,
    responses={
        201: {
            "description": "Scheduler created successfully",
            "content": {
                "application/json": {
                    "example": {
                        "id": "550e8400-e29b-41d4-a716-446655440000",
                        "name": "Daily Report Generator",
                        "frequency": "daily",
                        "time": "09:00",
                        "timezone": "America/New_York",
                        "is_active": True,
                        "workflow_id": "report-workflow-123",
                        "user_id": "user-123",
                        "created_at": "2025-01-17T10:30:00Z",
                        "updated_at": "2025-01-17T10:30:00Z",
                        "last_run_at": None,
                        "next_run_at": "2025-01-18T14:00:00Z",
                    }
                }
            },
        },
        400: {
            "description": "Invalid input data",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Invalid schedule configuration: time is required for daily frequency"
                    }
                }
            },
        },
        401: {
            "description": "Authentication required",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Authentication required. Provide Bearer token in Authorization header."
                    }
                }
            },
        },
    },
)
async def create_scheduler(
    request: Request,
    scheduler_data: SimplifiedSchedulerCreate = Body(
        ...,
        examples=SCHEDULER_CREATE_EXAMPLES,
        description="Scheduler configuration data including input_values for workflow parameters",
    ),
    db: AsyncSession = Depends(get_async_session),
):
    """Create a new scheduler."""
    try:
        # Extract user ID from bearer token
        user_id = await extract_user_id_from_request(request)

        scheduler_manager = SchedulerManager(db)
        scheduler = await scheduler_manager.create(scheduler_data, user_id)
        logger.info(f"Created scheduler {scheduler.id} for user {user_id}")
        return scheduler
    except ValueError as e:
        logger.error(f"Validation error creating scheduler: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating scheduler: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get(
    "/schedulers",
    response_model=List[SimplifiedSchedulerResponse],
    summary="List schedulers",
    description="""
    Retrieve a paginated list of schedulers for the authenticated user.

    This endpoint returns all schedulers belonging to the authenticated user
    with support for pagination to handle large numbers of schedulers efficiently.

    **Authentication**: Requires Bearer token in Authorization header.

    **Pagination**: Use `skip` and `limit` parameters to paginate through results.
    - `skip`: Number of schedulers to skip (for pagination)
    - `limit`: Maximum number of schedulers to return (1-1000)

    **Sorting**: Results are ordered by creation date (newest first).
    """,
    responses={
        200: {
            "description": "List of schedulers retrieved successfully",
            "content": {
                "application/json": {
                    "example": [
                        {
                            "id": "550e8400-e29b-41d4-a716-446655440000",
                            "name": "Daily Report Generator",
                            "frequency": "daily",
                            "time": "09:00",
                            "timezone": "America/New_York",
                            "is_active": True,
                            "workflow_id": "report-workflow-123",
                            "user_id": "user-123",
                            "created_at": "2025-01-17T10:30:00Z",
                            "updated_at": "2025-01-17T10:30:00Z",
                            "last_run_at": "2025-01-17T14:00:00Z",
                            "next_run_at": "2025-01-18T14:00:00Z",
                        }
                    ]
                }
            },
        }
    },
)
async def list_schedulers(
    request: Request,
    skip: int = Query(0, ge=0, description="Number of schedulers to skip"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of schedulers to return"
    ),
    db: AsyncSession = Depends(get_async_session),
):
    """List schedulers for a user."""
    try:
        # Extract user ID from bearer token
        user_id = await extract_user_id_from_request(request)

        scheduler_manager = SchedulerManager(db)
        schedulers = await scheduler_manager.list(
            user_id=user_id, skip=skip, limit=limit
        )
        logger.info(f"Listed {len(schedulers)} schedulers for user {user_id}")
        return schedulers
    except Exception as e:
        logger.error(f"Error listing schedulers: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get(
    "/schedulers/{scheduler_id}",
    response_model=SimplifiedSchedulerResponse,
    summary="Get scheduler details",
    description="""
    Retrieve detailed information about a specific scheduler.

    This endpoint returns complete information about a scheduler including
    its configuration, schedule details, and execution history.

    **Authentication**: Requires Bearer token in Authorization header.

    **Access Control**: Users can only access their own schedulers.
    """,
    responses={
        200: {
            "description": "Scheduler details retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "id": "550e8400-e29b-41d4-a716-446655440000",
                        "name": "Daily Report Generator",
                        "frequency": "daily",
                        "time": "09:00",
                        "timezone": "America/New_York",
                        "is_active": True,
                        "workflow_id": "report-workflow-123",
                        "user_id": "user-123",
                        "created_at": "2025-01-17T10:30:00Z",
                        "updated_at": "2025-01-17T10:30:00Z",
                        "last_run_at": "2025-01-17T14:00:00Z",
                        "next_run_at": "2025-01-18T14:00:00Z",
                    }
                }
            },
        },
        404: {
            "description": "Scheduler not found",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Scheduler 550e8400-e29b-41d4-a716-446655440000 not found"
                    }
                }
            },
        },
    },
)
async def get_scheduler(
    scheduler_id: str,
    request: Request,
    db: AsyncSession = Depends(get_async_session),
):
    """Get a specific scheduler."""
    try:
        # Extract user ID from bearer token
        user_id = await extract_user_id_from_request(request)

        scheduler_manager = SchedulerManager(db)
        scheduler = await scheduler_manager.get(scheduler_id, user_id)

        if not scheduler:
            raise HTTPException(
                status_code=404, detail=f"Scheduler {scheduler_id} not found"
            )

        logger.info(f"Retrieved scheduler {scheduler_id} for user {user_id}")
        return scheduler
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting scheduler {scheduler_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put(
    "/schedulers/{scheduler_id}",
    response_model=SimplifiedSchedulerResponse,
    summary="Update scheduler",
    description="""
    Update an existing scheduler configuration.

    This endpoint allows you to modify any aspect of a scheduler including
    its name, schedule configuration, timezone, and active status.

    **Authentication**: Requires Bearer token in Authorization header.

    **Access Control**: Users can only update their own schedulers.

    **Partial Updates**: Only provide the fields you want to update.
    All other fields will remain unchanged.

    **Schedule Recalculation**: If you update schedule-related fields
    (frequency, time, days, etc.), the next_run_at will be automatically
    recalculated based on the new schedule.
    """,
    responses={
        200: {
            "description": "Scheduler updated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "id": "550e8400-e29b-41d4-a716-446655440000",
                        "name": "Updated Daily Report Generator",
                        "frequency": "daily",
                        "time": "10:00",
                        "timezone": "America/New_York",
                        "is_active": True,
                        "workflow_id": "report-workflow-123",
                        "user_id": "user-123",
                        "created_at": "2025-01-17T10:30:00Z",
                        "updated_at": "2025-01-17T11:00:00Z",
                        "last_run_at": "2025-01-17T14:00:00Z",
                        "next_run_at": "2025-01-18T15:00:00Z",
                    }
                }
            },
        },
        404: {
            "description": "Scheduler not found",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Scheduler 550e8400-e29b-41d4-a716-446655440000 not found"
                    }
                }
            },
        },
    },
)
async def update_scheduler(
    scheduler_id: str,
    request: Request,
    scheduler_data: SimplifiedSchedulerUpdate = Body(
        ...,
        examples=SCHEDULER_UPDATE_EXAMPLES,
        description="Partial scheduler update data. Only provide fields you want to update.",
    ),
    db: AsyncSession = Depends(get_async_session),
):
    """Update a scheduler."""
    try:
        # Extract user ID from bearer token
        user_id = await extract_user_id_from_request(request)

        scheduler_manager = SchedulerManager(db)
        scheduler = await scheduler_manager.update(
            scheduler_id, scheduler_data, user_id
        )

        if not scheduler:
            raise HTTPException(
                status_code=404, detail=f"Scheduler {scheduler_id} not found"
            )

        logger.info(f"Updated scheduler {scheduler_id} for user {user_id}")
        return scheduler
    except HTTPException:
        raise
    except ValueError as e:
        logger.error(f"Validation error updating scheduler {scheduler_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating scheduler {scheduler_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete(
    "/schedulers/{scheduler_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete scheduler",
    description="""
    Permanently delete an existing scheduler.

    This endpoint permanently removes a scheduler and all its configuration.
    This action cannot be undone.

    **Authentication**: Requires Bearer token in Authorization header.

    **Access Control**: Users can only delete their own schedulers.

    **Warning**: Deleting a scheduler will stop all future executions.
    Any currently running executions will continue to completion.
    """,
    responses={
        204: {"description": "Scheduler deleted successfully"},
        404: {
            "description": "Scheduler not found",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Scheduler 550e8400-e29b-41d4-a716-446655440000 not found"
                    }
                }
            },
        },
    },
)
async def delete_scheduler(
    scheduler_id: str,
    request: Request,
    db: AsyncSession = Depends(get_async_session),
):
    """Delete a scheduler."""
    try:
        # Extract user ID from bearer token
        user_id = await extract_user_id_from_request(request)

        scheduler_manager = SchedulerManager(db)
        deleted = await scheduler_manager.delete(scheduler_id, user_id)

        if not deleted:
            raise HTTPException(
                status_code=404, detail=f"Scheduler {scheduler_id} not found"
            )

        logger.info(f"Deleted scheduler {scheduler_id} for user {user_id}")
        return None
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting scheduler {scheduler_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get(
    "/schedulers/due",
    response_model=List[SimplifiedSchedulerResponse],
    summary="Get due schedulers (Internal)",
    description="""
    Get schedulers that are currently due for execution.

    This is an internal endpoint that returns all schedulers
    across all users that are currently due for execution.

    **Authentication**: Requires Bearer token in Authorization header.

    **Use Case**: This endpoint is primarily used by the scheduler engine
    to identify which schedulers need to be executed.

    **Filtering**: Only returns active schedulers that have a next_run_at
    time that is in the past or current time.
    """,
    responses={
        200: {
            "description": "List of due schedulers retrieved successfully",
            "content": {
                "application/json": {
                    "example": [
                        {
                            "id": "550e8400-e29b-41d4-a716-446655440000",
                            "name": "Daily Report Generator",
                            "frequency": "daily",
                            "time": "09:00",
                            "timezone": "America/New_York",
                            "is_active": True,
                            "workflow_id": "report-workflow-123",
                            "user_id": "user-123",
                            "created_at": "2025-01-17T10:30:00Z",
                            "updated_at": "2025-01-17T10:30:00Z",
                            "last_run_at": "2025-01-16T14:00:00Z",
                            "next_run_at": "2025-01-17T14:00:00Z",
                        }
                    ]
                }
            },
        }
    },
)
async def get_due_schedulers(
    request: Request,
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of schedulers to return"
    ),
    offset: int = Query(0, ge=0, description="Number of schedulers to skip"),
    db: AsyncSession = Depends(get_async_session),
):
    """Get schedulers that are due for execution."""
    try:
        # Authenticate the request (this endpoint requires authentication)
        await extract_user_id_from_request(request)

        scheduler_manager = SchedulerManager(db)
        due_schedulers = await scheduler_manager.get_due_schedulers(
            limit=limit, offset=offset
        )
        logger.info(f"Retrieved {len(due_schedulers)} due schedulers")
        return due_schedulers
    except Exception as e:
        logger.error(f"Error getting due schedulers: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/schedulers/{scheduler_id}/activate",
    response_model=SimplifiedSchedulerResponse,
    summary="Activate scheduler",
    description="""
    Activate a scheduler to enable automatic execution.

    This endpoint sets the scheduler's `is_active` status to `true`,
    allowing it to be picked up by the scheduler engine for execution.

    **Authentication**: Requires Bearer token in Authorization header.

    **Access Control**: Users can only activate their own schedulers.

    **Effect**: Once activated, the scheduler will be executed according
    to its configured schedule.
    """,
    responses={
        200: {
            "description": "Scheduler activated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "id": "550e8400-e29b-41d4-a716-446655440000",
                        "name": "Daily Report Generator",
                        "frequency": "daily",
                        "time": "09:00",
                        "timezone": "America/New_York",
                        "is_active": True,
                        "workflow_id": "report-workflow-123",
                        "user_id": "user-123",
                        "created_at": "2025-01-17T10:30:00Z",
                        "updated_at": "2025-01-17T11:00:00Z",
                        "last_run_at": None,
                        "next_run_at": "2025-01-18T14:00:00Z",
                    }
                }
            },
        }
    },
)
async def activate_scheduler(
    scheduler_id: str,
    request: Request,
    db: AsyncSession = Depends(get_async_session),
):
    """Activate a scheduler."""
    try:
        # Extract user ID from bearer token
        user_id = await extract_user_id_from_request(request)

        scheduler_manager = SchedulerManager(db)
        update_data = SimplifiedSchedulerUpdate(is_active=True)
        scheduler = await scheduler_manager.update(scheduler_id, update_data, user_id)

        if not scheduler:
            raise HTTPException(
                status_code=404, detail=f"Scheduler {scheduler_id} not found"
            )

        logger.info(f"Activated scheduler {scheduler_id} for user {user_id}")
        return scheduler
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error activating scheduler {scheduler_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/schedulers/{scheduler_id}/deactivate",
    response_model=SimplifiedSchedulerResponse,
    summary="Deactivate scheduler",
    description="""
    Deactivate a scheduler to disable automatic execution.

    This endpoint sets the scheduler's `is_active` status to `false`,
    preventing it from being executed by the scheduler engine.

    **Authentication**: Requires Bearer token in Authorization header.

    **Access Control**: Users can only deactivate their own schedulers.

    **Effect**: Once deactivated, the scheduler will not be executed
    until it is activated again. The schedule configuration is preserved.
    """,
    responses={
        200: {
            "description": "Scheduler deactivated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "id": "550e8400-e29b-41d4-a716-446655440000",
                        "name": "Daily Report Generator",
                        "frequency": "daily",
                        "time": "09:00",
                        "timezone": "America/New_York",
                        "is_active": False,
                        "workflow_id": "report-workflow-123",
                        "user_id": "user-123",
                        "created_at": "2025-01-17T10:30:00Z",
                        "updated_at": "2025-01-17T11:00:00Z",
                        "last_run_at": "2025-01-17T14:00:00Z",
                        "next_run_at": "2025-01-18T14:00:00Z",
                    }
                }
            },
        }
    },
)
async def deactivate_scheduler(
    scheduler_id: str,
    request: Request,
    db: AsyncSession = Depends(get_async_session),
):
    """Deactivate a scheduler."""
    try:
        # Extract user ID from bearer token
        user_id = await extract_user_id_from_request(request)

        scheduler_manager = SchedulerManager(db)
        update_data = SimplifiedSchedulerUpdate(is_active=False)
        scheduler = await scheduler_manager.update(scheduler_id, update_data, user_id)

        if not scheduler:
            raise HTTPException(
                status_code=404, detail=f"Scheduler {scheduler_id} not found"
            )

        logger.info(f"Deactivated scheduler {scheduler_id} for user {user_id}")
        return scheduler
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deactivating scheduler {scheduler_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")
