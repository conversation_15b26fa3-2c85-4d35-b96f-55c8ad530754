"""
Main entry point for the Scheduler Service.

This module provides the main application entry point and FastAPI app setup
for the standalone scheduler service.
"""

import asyncio
import signal
import sys
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>earer

from src.api.routes import router as api_router
from src.core import SchedulerService
from src.database.connection import init_database, close_database
from src.utils.config import get_settings
from src.utils.database_init import initialize_database
from src.utils.logger import setup_logging, get_logger

# Global scheduler service instance
scheduler_service: SchedulerService = None


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan manager.

    Handles startup and shutdown events for the FastAPI application.
    """
    global scheduler_service

    settings = get_settings()
    logger = get_logger(__name__)

    # Startup
    logger.info("Starting Scheduler Service...")

    try:
        # Initialize database and create if not exists
        await initialize_database()
        logger.info("Database initialization completed")

        # Initialize database connection
        await init_database()
        logger.info("Database connection initialized")

        # Initialize scheduler service
        scheduler_service = SchedulerService(
            redis_url=settings.redis_url,
            max_concurrent_schedulers=settings.max_concurrent_schedulers,
            batch_size=settings.scheduler_batch_size,
            worker_concurrency=settings.task_worker_concurrency,
        )
        await scheduler_service.initialize()
        logger.info("Scheduler service initialized")

        # Start scheduler service
        await scheduler_service.start()
        logger.info("Scheduler service started")

        yield

    except Exception as e:
        logger.error(f"Failed to start scheduler service: {e}", exc_info=True)
        raise

    finally:
        # Shutdown
        logger.info("Shutting down Scheduler Service...")

        if scheduler_service:
            try:
                await scheduler_service.stop()
                logger.info("Scheduler service stopped")
            except Exception as e:
                logger.error(f"Error stopping scheduler service: {e}", exc_info=True)

        try:
            await close_database()
            logger.info("Database connections closed")
        except Exception as e:
            logger.error(f"Error closing database: {e}", exc_info=True)

        logger.info("Scheduler Service shutdown complete")


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.

    Returns:
        FastAPI: Configured FastAPI application instance
    """
    settings = get_settings()

    # Setup logging
    setup_logging(log_level=settings.log_level, log_format=settings.log_format)

    logger = get_logger(__name__)
    logger.info("Creating Scheduler Service application")

    # Create FastAPI app
    app = FastAPI(
        title="Scheduler Service",
        description="""
        A standalone, production-ready scheduler service for workflow automation.

        ## Features

        * **Multiple Schedule Types**: Support for every minute, hourly, daily, weekly, monthly, and custom cron schedules
        * **Timezone Support**: Full timezone support with automatic DST handling
        * **Bearer Token Authentication**: Secure authentication with external auth service
        * **RESTful API**: Complete REST API for scheduler management
        * **Production Ready**: Built for scalability and reliability

        ## Authentication

        This API uses **Bearer Token** authentication:

        - Include `Authorization: Bearer <token>` header in all requests
        - Tokens are validated with the external authentication service
        - Each user can only access their own schedulers

        ## Schedule Types

        * `every_minute` - Executes every minute
        * `hourly` - Executes every hour
        * `daily` - Executes daily at specified time
        * `weekly` - Executes on specified days of the week
        * `monthly` - Executes on specified days of the month
        * `custom` - Uses cron expression for complex scheduling

        ## Rate Limits

        * All endpoints: 1000 requests per hour per user
        """,
        version=settings.version,
        lifespan=lifespan,
        docs_url="/docs",
        redoc_url="/redoc",
        contact={
            "name": "Scheduler Service Support",
            "email": "<EMAIL>",
        },
        license_info={
            "name": "MIT License",
            "url": "https://opensource.org/licenses/MIT",
        },
        servers=[
            {
                "url": f"http://{settings.host}:{settings.port}",
                "description": "Current server",
            }
        ],
        tags_metadata=[
            {
                "name": "schedulers",
                "description": "Operations with schedulers. Create, read, update, and delete schedulers.",
            },
            {
                "name": "health",
                "description": "Health check and service status endpoints.",
            },
        ],
    )

    # Configure OpenAPI security scheme
    if not hasattr(app, "openapi_schema") or app.openapi_schema is None:

        def custom_openapi():
            if app.openapi_schema:
                return app.openapi_schema

            from fastapi.openapi.utils import get_openapi

            openapi_schema = get_openapi(
                title=app.title,
                version=app.version,
                description=app.description,
                routes=app.routes,
            )

            # Add security scheme
            openapi_schema["components"]["securitySchemes"] = {
                "BearerAuth": {
                    "type": "http",
                    "scheme": "bearer",
                    "bearerFormat": "JWT",
                    "description": "Enter your bearer token in the format: Bearer <token>",
                }
            }

            # Apply security to all endpoints except health and root
            for path, path_item in openapi_schema["paths"].items():
                if path not in ["/health", "/"]:
                    for method, operation in path_item.items():
                        if method.lower() in ["get", "post", "put", "delete", "patch"]:
                            operation["security"] = [{"BearerAuth": []}]

            app.openapi_schema = openapi_schema
            return app.openapi_schema

        app.openapi = custom_openapi

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"] if settings.debug else [],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include API routes
    app.include_router(api_router, prefix="/api/v1")

    # Health check endpoint
    @app.get(
        "/health",
        tags=["health"],
        summary="Health check",
        description="""
        Check the health status of the scheduler service.

        This endpoint performs comprehensive health checks including:
        * Database connectivity
        * Redis connectivity (if enabled)
        * Scheduler service status
        * Instance coordination status

        Returns HTTP 200 for healthy service, HTTP 503 for unhealthy service.
        """,
        responses={
            200: {
                "description": "Service is healthy",
                "content": {
                    "application/json": {
                        "example": {
                            "status": "healthy",
                            "service": "scheduler-service",
                            "version": "0.1.0",
                            "scheduler_service": "healthy",
                        }
                    }
                },
            },
            503: {
                "description": "Service is unhealthy",
                "content": {
                    "application/json": {
                        "example": {
                            "status": "unhealthy",
                            "service": "scheduler-service",
                            "version": "0.1.0",
                            "scheduler_service": "unhealthy",
                        }
                    }
                },
            },
        },
    )
    async def health_check():
        """Health check endpoint."""
        global scheduler_service

        health_status = {
            "status": "healthy",
            "service": "scheduler-service",
            "version": settings.version,
        }

        # Check scheduler service health
        if scheduler_service:
            try:
                is_healthy = await scheduler_service.health_check()
                if not is_healthy:
                    health_status["status"] = "unhealthy"
                    health_status["scheduler_service"] = "unhealthy"
            except Exception as e:
                health_status["status"] = "unhealthy"
                health_status["scheduler_service"] = f"error: {str(e)}"
        else:
            health_status["status"] = "unhealthy"
            health_status["scheduler_service"] = "not_initialized"

        status_code = 200 if health_status["status"] == "healthy" else 503
        return JSONResponse(content=health_status, status_code=status_code)

    # Root endpoint
    @app.get(
        "/",
        tags=["health"],
        summary="Service information",
        description="""
        Get basic information about the scheduler service.

        This endpoint provides service metadata including version,
        status, and links to documentation.
        """,
        responses={
            200: {
                "description": "Service information",
                "content": {
                    "application/json": {
                        "example": {
                            "service": "scheduler-service",
                            "version": "0.1.0",
                            "status": "running",
                            "docs": "/docs",
                        }
                    }
                },
            }
        },
    )
    async def root():
        """Root endpoint with service information."""
        return {
            "service": "scheduler-service",
            "version": settings.version,
            "status": "running",
            "docs": "/docs",
        }

    return app


def main():
    """
    Main entry point for the scheduler service.

    This function is called when the service is started via the CLI.
    """
    import uvicorn

    settings = get_settings()

    # Setup logging
    setup_logging(log_level=settings.log_level, log_format=settings.log_format)

    logger = get_logger(__name__)
    logger.info("Starting Scheduler Service main application")

    # Create app
    app = create_app()

    # Run with uvicorn
    uvicorn.run(
        app,
        host=settings.host,
        port=settings.port,
        log_level=settings.log_level.lower(),
        access_log=settings.debug,
        reload=settings.debug,
    )


if __name__ == "__main__":
    main()
