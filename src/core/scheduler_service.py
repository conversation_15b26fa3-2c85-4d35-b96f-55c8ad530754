"""
Main Scheduler Service orchestrator.

This module provides the main service class that coordinates all scheduler
components including the engine, task queue, and distributed locking.
"""

import asyncio
from typing import Optional

from sqlalchemy.ext.asyncio import AsyncSession

from src.core.distributed_lock import LockManager
from src.core.instance_coordinator import InstanceCoordinator
from src.core.metrics import SchedulerMetrics, get_global_metrics
from src.core.scheduler_engine import SchedulerEngine
from src.core.task_queue import RedisTaskQueue
from src.database.connection import get_database_manager
from src.utils.config import get_settings
from src.utils.logger import get_logger

logger = get_logger(__name__)


class SchedulerService:
    """
    Main scheduler service orchestrator.

    Coordinates all scheduler components and provides the main service
    interface for running the scheduler engine.
    """

    def __init__(
        self,
        redis_url: Optional[str] = None,
        max_concurrent_schedulers: int = 100,
        batch_size: int = 50,
        worker_concurrency: int = 5,
    ):
        """
        Initialize the scheduler service.

        Args:
            redis_url: Redis connection URL
            max_concurrent_schedulers: Maximum concurrent scheduler processing
            batch_size: Batch size for processing schedulers
            worker_concurrency: Task worker concurrency level
        """
        self.settings = get_settings()
        self.redis_url = redis_url or self.settings.redis_url
        self.max_concurrent_schedulers = max_concurrent_schedulers
        self.batch_size = batch_size
        self.worker_concurrency = worker_concurrency

        # Components
        self.db_manager = get_database_manager()
        self.lock_manager: Optional[LockManager] = None
        self.task_queue: Optional[RedisTaskQueue] = None
        self.instance_coordinator: Optional[InstanceCoordinator] = None
        self.scheduler_engine: Optional[SchedulerEngine] = None
        self.metrics = get_global_metrics()

        # Control
        self._is_running = False
        self._scheduler_task: Optional[asyncio.Task] = None

    async def initialize(self):
        """Initialize all service components."""
        logger.info("Initializing Scheduler Service components...")

        try:
            # Initialize distributed locking
            if self.settings.enable_distributed_locking:
                self.lock_manager = LockManager(self.redis_url)
                logger.info("Distributed locking initialized")
            else:
                logger.warning("Distributed locking is disabled")

            # Initialize task queue
            if self.settings.enable_task_queue:
                self.task_queue = RedisTaskQueue(self.redis_url)
                logger.info("Task queue initialized")
            else:
                logger.warning("Task queue is disabled - using direct execution")

            # Initialize instance coordinator
            self.instance_coordinator = InstanceCoordinator(self.redis_url)
            logger.info("Instance coordinator initialized")

            # Initialize scheduler engine
            async with self.db_manager.get_async_session() as session:
                self.scheduler_engine = SchedulerEngine(
                    db_session=session,
                    task_queue=self.task_queue,
                    lock_manager=self.lock_manager,
                    batch_size=self.batch_size,
                    max_concurrency=self.max_concurrent_schedulers,
                    instance_coordinator=self.instance_coordinator,
                )
                logger.info("Scheduler engine initialized")

            logger.info("Scheduler Service initialization complete")

        except Exception as e:
            logger.error(f"Failed to initialize Scheduler Service: {e}", exc_info=True)
            raise

    async def start(self):
        """Start the scheduler service."""
        if self._is_running:
            logger.warning("Scheduler Service is already running")
            return

        logger.info("Starting Scheduler Service...")

        try:
            # Start instance coordinator
            if self.instance_coordinator:
                await self.instance_coordinator.start()

            # Start the scheduler loop
            self._is_running = True
            self._scheduler_task = asyncio.create_task(self._scheduler_loop())

            logger.info("Scheduler Service started successfully")

        except Exception as e:
            logger.error(f"Failed to start Scheduler Service: {e}", exc_info=True)
            self._is_running = False
            raise

    async def stop(self):
        """Stop the scheduler service."""
        if not self._is_running:
            logger.warning("Scheduler Service is not running")
            return

        logger.info("Stopping Scheduler Service...")

        try:
            # Stop the scheduler loop
            self._is_running = False

            if self._scheduler_task:
                self._scheduler_task.cancel()
                try:
                    await self._scheduler_task
                except asyncio.CancelledError:
                    pass
                self._scheduler_task = None

            # Stop instance coordinator
            if self.instance_coordinator:
                await self.instance_coordinator.stop()

            # Close connections
            if self.task_queue:
                await self.task_queue.close()

            if self.lock_manager:
                await self.lock_manager.close()

            logger.info("Scheduler Service stopped successfully")

        except Exception as e:
            logger.error(f"Error stopping Scheduler Service: {e}", exc_info=True)

    async def _scheduler_loop(self):
        """Main scheduler processing loop."""
        logger.info("Starting scheduler processing loop...")

        cycle_interval = self.settings.scheduler_cycle_interval

        while self._is_running:
            try:
                # Check if this instance should process schedulers
                should_process = True

                if self.instance_coordinator:
                    # Only process if this is the leader instance
                    should_process = await self.instance_coordinator.is_leader()
                    if not should_process:
                        logger.debug(
                            "Not the leader instance, skipping scheduler processing"
                        )

                if should_process and self.scheduler_engine:
                    # Create a new database session for this cycle
                    async with self.db_manager.get_async_session() as session:
                        # Update the scheduler engine with the new session
                        self.scheduler_engine.db_session = session

                        # Process due schedulers
                        await self.scheduler_engine.process_due_schedulers()

                # Wait for next cycle
                await asyncio.sleep(cycle_interval)

            except asyncio.CancelledError:
                logger.info("Scheduler loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in scheduler loop: {e}", exc_info=True)
                # Continue running even if there's an error
                await asyncio.sleep(cycle_interval)

        logger.info("Scheduler processing loop stopped")

    async def health_check(self) -> bool:
        """
        Perform a comprehensive health check.

        Returns:
            bool: True if all components are healthy
        """
        try:
            health_status = True

            # Check database connectivity
            if not await self.db_manager.test_connection():
                logger.error("Database health check failed")
                health_status = False

            # Check Redis connectivity (if enabled)
            if self.settings.enable_task_queue and self.task_queue:
                try:
                    import redis.asyncio as redis

                    redis_client = redis.from_url(self.redis_url)
                    await redis_client.ping()
                    await redis_client.close()
                except Exception as e:
                    logger.error(f"Redis health check failed: {e}")
                    health_status = False

            # Check instance coordinator
            if self.instance_coordinator:
                if not await self.instance_coordinator.health_check():
                    logger.error("Instance coordinator health check failed")
                    health_status = False

            return health_status

        except Exception as e:
            logger.error(f"Health check failed: {e}", exc_info=True)
            return False

    async def get_status(self) -> dict:
        """
        Get comprehensive service status.

        Returns:
            dict: Service status information
        """
        try:
            status = {
                "service": "scheduler-service",
                "version": self.settings.version,
                "is_running": self._is_running,
                "instance_id": (
                    self.instance_coordinator.instance_id
                    if self.instance_coordinator
                    else None
                ),
                "components": {
                    "database": await self.db_manager.test_connection(),
                    "task_queue": self.task_queue is not None,
                    "distributed_locking": self.lock_manager is not None,
                    "instance_coordinator": self.instance_coordinator is not None,
                },
                "metrics": self.metrics.get_metrics_summary(),
            }

            # Add instance information if available
            if self.instance_coordinator:
                status["instances"] = {
                    "active_count": await self.instance_coordinator.get_instance_count(),
                    "is_leader": await self.instance_coordinator.is_leader(),
                    "active_instances": await self.instance_coordinator.get_active_instances(),
                }

            return status

        except Exception as e:
            logger.error(f"Error getting service status: {e}", exc_info=True)
            return {
                "service": "scheduler-service",
                "error": str(e),
                "is_running": self._is_running,
            }

    def is_running(self) -> bool:
        """Check if the service is currently running."""
        return self._is_running
