"""
Instance coordination for distributed scheduler service.

This module provides coordination capabilities for multiple scheduler
service instances to ensure proper load distribution and failover.
"""

import asyncio
import json
import socket
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set

import redis.asyncio as redis

from src.utils.config import get_settings
from src.utils.logger import get_logger

logger = get_logger(__name__)


class InstanceCoordinator:
    """
    Coordinates multiple scheduler service instances.

    Provides service discovery, health monitoring, and load balancing
    capabilities for distributed scheduler deployments.
    """

    def __init__(
        self, redis_url: Optional[str] = None, instance_id: Optional[str] = None
    ):
        """
        Initialize the instance coordinator.

        Args:
            redis_url: Redis connection URL
            instance_id: Unique instance identifier
        """
        settings = get_settings()
        self.redis_url = redis_url or settings.redis_url
        self.instance_id = instance_id or settings.instance_id or str(uuid.uuid4())
        self.hostname = settings.hostname
        self.port = settings.port
        self.version = settings.version

        self._redis_client: Optional[redis.Redis] = None
        self._heartbeat_task: Optional[asyncio.Task] = None
        self._cleanup_task: Optional[asyncio.Task] = None
        self._is_running = False

        # Instance metadata
        self.instance_info = {
            "instance_id": self.instance_id,
            "hostname": self.hostname,
            "port": self.port,
            "version": self.version,
            "started_at": datetime.utcnow().isoformat(),
            "last_heartbeat": None,
            "status": "starting",
        }

    async def _get_redis_client(self) -> redis.Redis:
        """Get or create Redis client."""
        if self._redis_client is None:
            self._redis_client = redis.from_url(self.redis_url)
        return self._redis_client

    async def start(self):
        """Start the instance coordinator."""
        if self._is_running:
            return

        self._is_running = True
        logger.info(f"Starting instance coordinator for {self.instance_id}")

        # Register this instance
        await self._register_instance()

        # Start background tasks
        self._heartbeat_task = asyncio.create_task(self._heartbeat_loop())
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())

        logger.info(f"Instance coordinator started for {self.instance_id}")

    async def stop(self):
        """Stop the instance coordinator."""
        if not self._is_running:
            return

        self._is_running = False
        logger.info(f"Stopping instance coordinator for {self.instance_id}")

        # Cancel background tasks
        if self._heartbeat_task:
            self._heartbeat_task.cancel()
            try:
                await self._heartbeat_task
            except asyncio.CancelledError:
                pass

        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass

        # Unregister this instance
        await self._unregister_instance()

        # Close Redis connection
        if self._redis_client:
            await self._redis_client.close()
            self._redis_client = None

        logger.info(f"Instance coordinator stopped for {self.instance_id}")

    async def _register_instance(self):
        """Register this instance in Redis."""
        try:
            redis_client = await self._get_redis_client()
            instance_key = f"scheduler_instances:{self.instance_id}"

            self.instance_info["status"] = "running"
            self.instance_info["last_heartbeat"] = datetime.utcnow().isoformat()

            # Store instance info with TTL
            await redis_client.setex(
                instance_key, 300, json.dumps(self.instance_info)  # 5 minutes TTL
            )

            # Add to active instances set
            await redis_client.sadd("scheduler_active_instances", self.instance_id)

            logger.info(f"Registered instance {self.instance_id}")

        except Exception as e:
            logger.error(f"Error registering instance {self.instance_id}: {e}")

    async def _unregister_instance(self):
        """Unregister this instance from Redis."""
        try:
            redis_client = await self._get_redis_client()
            instance_key = f"scheduler_instances:{self.instance_id}"

            # Remove instance info
            await redis_client.delete(instance_key)

            # Remove from active instances set
            await redis_client.srem("scheduler_active_instances", self.instance_id)

            logger.info(f"Unregistered instance {self.instance_id}")

        except Exception as e:
            logger.error(f"Error unregistering instance {self.instance_id}: {e}")

    async def _heartbeat_loop(self):
        """Send periodic heartbeats."""
        while self._is_running:
            try:
                await self._send_heartbeat()
                await asyncio.sleep(30)  # Heartbeat every 30 seconds
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in heartbeat loop: {e}")
                await asyncio.sleep(30)

    async def _send_heartbeat(self):
        """Send a heartbeat to indicate this instance is alive."""
        try:
            redis_client = await self._get_redis_client()
            instance_key = f"scheduler_instances:{self.instance_id}"

            self.instance_info["last_heartbeat"] = datetime.utcnow().isoformat()

            # Update instance info with fresh TTL
            await redis_client.setex(
                instance_key, 300, json.dumps(self.instance_info)  # 5 minutes TTL
            )

            logger.debug(f"Sent heartbeat for instance {self.instance_id}")

        except Exception as e:
            logger.error(f"Error sending heartbeat for {self.instance_id}: {e}")

    async def _cleanup_loop(self):
        """Clean up stale instances."""
        while self._is_running:
            try:
                await self._cleanup_stale_instances()
                await asyncio.sleep(60)  # Cleanup every minute
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
                await asyncio.sleep(60)

    async def _cleanup_stale_instances(self):
        """Remove stale instances that haven't sent heartbeats."""
        try:
            redis_client = await self._get_redis_client()

            # Get all active instances
            active_instances = await redis_client.smembers("scheduler_active_instances")

            for instance_id_bytes in active_instances:
                instance_id = instance_id_bytes.decode()
                instance_key = f"scheduler_instances:{instance_id}"

                # Check if instance info exists
                instance_data = await redis_client.get(instance_key)
                if not instance_data:
                    # Instance info expired, remove from active set
                    await redis_client.srem("scheduler_active_instances", instance_id)
                    logger.info(f"Cleaned up stale instance {instance_id}")

        except Exception as e:
            logger.error(f"Error cleaning up stale instances: {e}")

    async def get_active_instances(self) -> List[Dict]:
        """
        Get list of active scheduler instances.

        Returns:
            List[Dict]: List of active instance information
        """
        try:
            redis_client = await self._get_redis_client()

            # Get all active instances
            active_instances = await redis_client.smembers("scheduler_active_instances")

            instances = []
            for instance_id_bytes in active_instances:
                instance_id = instance_id_bytes.decode()
                instance_key = f"scheduler_instances:{instance_id}"

                instance_data = await redis_client.get(instance_key)
                if instance_data:
                    instance_info = json.loads(instance_data)
                    instances.append(instance_info)

            return instances

        except Exception as e:
            logger.error(f"Error getting active instances: {e}")
            return []

    async def is_leader(self) -> bool:
        """
        Check if this instance is the leader.

        Simple leader election based on lexicographically smallest instance ID.

        Returns:
            bool: True if this instance is the leader
        """
        try:
            active_instances = await self.get_active_instances()
            if not active_instances:
                return True  # Only instance, so it's the leader

            # Sort by instance_id and check if this is the first one
            sorted_instances = sorted(active_instances, key=lambda x: x["instance_id"])
            return sorted_instances[0]["instance_id"] == self.instance_id

        except Exception as e:
            logger.error(f"Error checking leader status: {e}")
            return False

    async def get_instance_count(self) -> int:
        """
        Get the number of active instances.

        Returns:
            int: Number of active instances
        """
        try:
            redis_client = await self._get_redis_client()
            return await redis_client.scard("scheduler_active_instances")
        except Exception as e:
            logger.error(f"Error getting instance count: {e}")
            return 0

    async def health_check(self) -> bool:
        """
        Perform health check for this instance.

        Returns:
            bool: True if instance is healthy
        """
        try:
            # Check Redis connectivity
            redis_client = await self._get_redis_client()
            await redis_client.ping()

            # Check if instance is properly registered
            instance_key = f"scheduler_instances:{self.instance_id}"
            exists = await redis_client.exists(instance_key)

            return bool(exists)

        except Exception as e:
            logger.error(f"Health check failed for {self.instance_id}: {e}")
            return False
