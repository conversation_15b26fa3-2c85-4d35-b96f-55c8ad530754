"""
Metrics collection for the Scheduler Service.

This module provides metrics collection and monitoring capabilities
for scheduler performance and health monitoring.
"""

import time
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime, timedelta

from src.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class SchedulerMetrics:
    """
    Metrics collector for scheduler operations.

    Tracks performance metrics, execution counts, and error rates
    for monitoring and alerting.
    """

    # Execution metrics
    total_executions: int = 0
    successful_executions: int = 0
    failed_executions: int = 0

    # Timing metrics
    total_execution_time: float = 0.0
    min_execution_time: Optional[float] = None
    max_execution_time: Optional[float] = None

    # Batch processing metrics
    total_batches: int = 0
    total_schedulers_processed: int = 0

    # Error tracking
    error_count: int = 0
    last_error: Optional[str] = None
    last_error_time: Optional[datetime] = None

    # Performance tracking
    execution_times: list = field(default_factory=list)

    def record_batch_execution(
        self, total: int, successful: int, failed: int, execution_time: float
    ):
        """
        Record metrics for a batch execution.

        Args:
            total: Total number of schedulers processed
            successful: Number of successful executions
            failed: Number of failed executions
            execution_time: Time taken for the batch
        """
        self.total_batches += 1
        self.total_schedulers_processed += total
        self.successful_executions += successful
        self.failed_executions += failed

        # Update timing metrics
        self.total_execution_time += execution_time

        if self.min_execution_time is None or execution_time < self.min_execution_time:
            self.min_execution_time = execution_time

        if self.max_execution_time is None or execution_time > self.max_execution_time:
            self.max_execution_time = execution_time

        # Keep last 100 execution times for analysis
        self.execution_times.append(execution_time)
        if len(self.execution_times) > 100:
            self.execution_times.pop(0)

        logger.debug(
            f"Recorded batch metrics: {total} total, {successful} successful, "
            f"{failed} failed, {execution_time:.2f}s"
        )

    def record_engine_error(self, error_message: str):
        """
        Record an engine-level error.

        Args:
            error_message: Error message to record
        """
        self.error_count += 1
        self.last_error = error_message
        self.last_error_time = datetime.utcnow()

        logger.debug(f"Recorded engine error: {error_message}")

    def get_success_rate(self) -> float:
        """
        Calculate the success rate of scheduler executions.

        Returns:
            float: Success rate as a percentage (0-100)
        """
        total = self.successful_executions + self.failed_executions
        if total == 0:
            return 100.0
        return (self.successful_executions / total) * 100.0

    def get_average_execution_time(self) -> float:
        """
        Calculate the average execution time.

        Returns:
            float: Average execution time in seconds
        """
        if self.total_batches == 0:
            return 0.0
        return self.total_execution_time / self.total_batches

    def get_recent_average_execution_time(self, count: int = 10) -> float:
        """
        Calculate the average execution time for recent batches.

        Args:
            count: Number of recent executions to consider

        Returns:
            float: Recent average execution time in seconds
        """
        if not self.execution_times:
            return 0.0

        recent_times = self.execution_times[-count:]
        return sum(recent_times) / len(recent_times)

    def get_throughput(self, time_window_minutes: int = 60) -> float:
        """
        Calculate scheduler processing throughput.

        Args:
            time_window_minutes: Time window for throughput calculation

        Returns:
            float: Schedulers processed per minute
        """
        if self.total_batches == 0:
            return 0.0

        # This is a simplified calculation
        # In a real implementation, you'd track timestamps
        return self.total_schedulers_processed / time_window_minutes

    def get_metrics_summary(self) -> Dict[str, Any]:
        """
        Get a comprehensive metrics summary.

        Returns:
            Dict[str, Any]: Metrics summary dictionary
        """
        return {
            "execution_metrics": {
                "total_executions": self.total_executions,
                "successful_executions": self.successful_executions,
                "failed_executions": self.failed_executions,
                "success_rate_percent": round(self.get_success_rate(), 2),
            },
            "timing_metrics": {
                "total_execution_time": round(self.total_execution_time, 2),
                "average_execution_time": round(self.get_average_execution_time(), 2),
                "min_execution_time": self.min_execution_time,
                "max_execution_time": self.max_execution_time,
                "recent_average_execution_time": round(
                    self.get_recent_average_execution_time(), 2
                ),
            },
            "batch_metrics": {
                "total_batches": self.total_batches,
                "total_schedulers_processed": self.total_schedulers_processed,
                "average_schedulers_per_batch": (
                    round(self.total_schedulers_processed / self.total_batches, 2)
                    if self.total_batches > 0
                    else 0
                ),
            },
            "error_metrics": {
                "error_count": self.error_count,
                "last_error": self.last_error,
                "last_error_time": (
                    self.last_error_time.isoformat() if self.last_error_time else None
                ),
            },
            "performance_metrics": {
                "throughput_per_minute": round(self.get_throughput(), 2),
            },
        }

    def reset_metrics(self):
        """Reset all metrics to initial state."""
        self.total_executions = 0
        self.successful_executions = 0
        self.failed_executions = 0
        self.total_execution_time = 0.0
        self.min_execution_time = None
        self.max_execution_time = None
        self.total_batches = 0
        self.total_schedulers_processed = 0
        self.error_count = 0
        self.last_error = None
        self.last_error_time = None
        self.execution_times.clear()

        logger.info("Scheduler metrics reset")

    def __str__(self) -> str:
        """String representation of metrics."""
        return (
            f"SchedulerMetrics("
            f"batches={self.total_batches}, "
            f"processed={self.total_schedulers_processed}, "
            f"success_rate={self.get_success_rate():.1f}%, "
            f"avg_time={self.get_average_execution_time():.2f}s"
            f")"
        )


# Global metrics instance
_global_metrics: Optional[SchedulerMetrics] = None


def get_global_metrics() -> SchedulerMetrics:
    """
    Get the global metrics instance.

    Returns:
        SchedulerMetrics: Global metrics collector
    """
    global _global_metrics
    if _global_metrics is None:
        _global_metrics = SchedulerMetrics()
    return _global_metrics


def reset_global_metrics():
    """Reset the global metrics instance."""
    global _global_metrics
    if _global_metrics:
        _global_metrics.reset_metrics()
    else:
        _global_metrics = SchedulerMetrics()
