"""
Workflow execution client for the Scheduler Service.

This module provides functionality to execute workflows by calling
the external workflow service API.
"""

import asyncio
from typing import Dict, Any, Optional

import httpx
from sqlalchemy.ext.asyncio import AsyncSession

from src.utils.config import get_settings
from src.utils.logger import get_logger

logger = get_logger(__name__)


class WorkflowExecutor:
    """
    Client for executing workflows via the workflow service API.

    Handles communication with the external workflow service to trigger
    workflow executions when schedulers fire.
    """

    def __init__(self, db_session: AsyncSession):
        """
        Initialize the workflow executor.

        Args:
            db_session: Database session for any needed database operations
        """
        self.db_session = db_session
        self.settings = get_settings()
        self.workflow_service_url = self.settings.workflow_service_url
        self.workflow_service_api_key = self.settings.workflow_service_api_key
        self.timeout = self.settings.workflow_service_timeout

    async def execute_workflow(
        self, workflow_id: str, user_id: str, event_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Execute a workflow via the workflow service API.

        Args:
            workflow_id: ID of the workflow to execute
            user_id: ID of the user who owns the workflow
            event_data: Event data to pass to the workflow

        Returns:
            Dict[str, Any]: Workflow execution result

        Raises:
            Exception: If workflow execution fails
        """
        try:
            # Prepare the request payload
            payload = {
                "workflow_id": workflow_id,
                "user_id": user_id,
                "event_data": event_data,
                "source": "scheduler_service",
                "execution_type": "scheduled",
            }

            # Prepare headers
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.workflow_service_api_key}",
                "X-Source": "scheduler-service",
            }

            # Make the API call
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                logger.info(f"Executing workflow {workflow_id} for user {user_id}")

                response = await client.post(
                    f"{self.workflow_service_url}/api/v1/workflows/execute",
                    json=payload,
                    headers=headers,
                )

                # Check response status
                if response.status_code == 200:
                    result = response.json()
                    logger.info(
                        f"Successfully executed workflow {workflow_id} for user {user_id}"
                    )
                    return result
                elif response.status_code == 404:
                    error_msg = f"Workflow {workflow_id} not found"
                    logger.error(error_msg)
                    raise Exception(error_msg)
                elif response.status_code == 403:
                    error_msg = f"Access denied for workflow {workflow_id}"
                    logger.error(error_msg)
                    raise Exception(error_msg)
                else:
                    error_msg = (
                        f"Workflow execution failed with status {response.status_code}: "
                        f"{response.text}"
                    )
                    logger.error(error_msg)
                    raise Exception(error_msg)

        except httpx.TimeoutException:
            error_msg = f"Timeout executing workflow {workflow_id}"
            logger.error(error_msg)
            raise Exception(error_msg)
        except httpx.RequestError as e:
            error_msg = f"Request error executing workflow {workflow_id}: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
        except Exception as e:
            error_msg = f"Unexpected error executing workflow {workflow_id}: {e}"
            logger.error(error_msg, exc_info=True)
            raise Exception(error_msg)

    async def validate_workflow(self, workflow_id: str, user_id: str) -> bool:
        """
        Validate that a workflow exists and is accessible.

        Args:
            workflow_id: ID of the workflow to validate
            user_id: ID of the user who owns the workflow

        Returns:
            bool: True if workflow is valid and accessible
        """
        try:
            # Prepare headers
            headers = {
                "Authorization": f"Bearer {self.workflow_service_api_key}",
                "X-Source": "scheduler-service",
            }

            # Make the API call
            async with httpx.AsyncClient(timeout=10) as client:
                response = await client.get(
                    f"{self.workflow_service_url}/api/v1/workflows/{workflow_id}",
                    headers=headers,
                    params={"user_id": user_id},
                )

                if response.status_code == 200:
                    workflow_data = response.json()
                    # Check if workflow is active
                    is_active = workflow_data.get("is_active", False)
                    if not is_active:
                        logger.warning(f"Workflow {workflow_id} is not active")
                        return False

                    logger.debug(f"Workflow {workflow_id} validation successful")
                    return True
                elif response.status_code == 404:
                    logger.warning(f"Workflow {workflow_id} not found")
                    return False
                elif response.status_code == 403:
                    logger.warning(f"Access denied for workflow {workflow_id}")
                    return False
                else:
                    logger.error(
                        f"Workflow validation failed with status {response.status_code}"
                    )
                    return False

        except Exception as e:
            logger.error(f"Error validating workflow {workflow_id}: {e}")
            return False

    async def get_workflow_info(
        self, workflow_id: str, user_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get workflow information from the workflow service.

        Args:
            workflow_id: ID of the workflow
            user_id: ID of the user who owns the workflow

        Returns:
            Optional[Dict[str, Any]]: Workflow information or None if not found
        """
        try:
            # Prepare headers
            headers = {
                "Authorization": f"Bearer {self.workflow_service_api_key}",
                "X-Source": "scheduler-service",
            }

            # Make the API call
            async with httpx.AsyncClient(timeout=10) as client:
                response = await client.get(
                    f"{self.workflow_service_url}/api/v1/workflows/{workflow_id}",
                    headers=headers,
                    params={"user_id": user_id},
                )

                if response.status_code == 200:
                    return response.json()
                else:
                    logger.warning(
                        f"Failed to get workflow info for {workflow_id}: "
                        f"status {response.status_code}"
                    )
                    return None

        except Exception as e:
            logger.error(f"Error getting workflow info for {workflow_id}: {e}")
            return None

    async def health_check(self) -> bool:
        """
        Check if the workflow service is healthy and accessible.

        Returns:
            bool: True if workflow service is healthy
        """
        try:
            # Prepare headers
            headers = {
                "Authorization": f"Bearer {self.workflow_service_api_key}",
                "X-Source": "scheduler-service",
            }

            # Make a health check call
            async with httpx.AsyncClient(timeout=5) as client:
                response = await client.get(
                    f"{self.workflow_service_url}/health", headers=headers
                )

                return response.status_code == 200

        except Exception as e:
            logger.error(f"Workflow service health check failed: {e}")
            return False
