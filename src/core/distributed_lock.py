"""
Distributed locking implementation using Redis.

This module provides distributed locking capabilities to ensure that
only one instance processes a scheduler at a time.
"""

import asyncio
import time
from typing import Optional

import redis.asyncio as redis

from src.utils.config import get_settings
from src.utils.logger import get_logger

logger = get_logger(__name__)


class LockManager:
    """
    Redis-based distributed lock manager.

    Provides distributed locking capabilities to prevent race conditions
    when multiple instances are processing schedulers.
    """

    def __init__(self, redis_url: Optional[str] = None):
        """
        Initialize the lock manager.

        Args:
            redis_url: Redis connection URL. If None, uses settings.
        """
        settings = get_settings()
        self.redis_url = redis_url or settings.distributed_lock_redis_url
        self.default_ttl = settings.distributed_lock_default_ttl
        self.retry_delay = settings.distributed_lock_retry_delay
        self.max_retries = settings.distributed_lock_max_retries
        self._redis_client: Optional[redis.Redis] = None

    async def _get_redis_client(self) -> redis.Redis:
        """Get or create Redis client."""
        if self._redis_client is None:
            self._redis_client = redis.from_url(self.redis_url)
        return self._redis_client

    async def acquire_scheduler_lock(
        self,
        scheduler_id: str,
        ttl: Optional[int] = None,
        timeout: Optional[float] = None,
    ) -> bool:
        """
        Acquire a lock for a specific scheduler.

        Args:
            scheduler_id: ID of the scheduler to lock
            ttl: Time-to-live for the lock in seconds
            timeout: Maximum time to wait for lock acquisition

        Returns:
            bool: True if lock was acquired, False otherwise
        """
        lock_key = f"scheduler_lock:{scheduler_id}"
        lock_value = f"{time.time()}"
        ttl = ttl or self.default_ttl

        try:
            redis_client = await self._get_redis_client()

            # Try to acquire lock with SET NX EX
            result = await redis_client.set(
                lock_key,
                lock_value,
                nx=True,  # Only set if key doesn't exist
                ex=ttl,  # Set expiration time
            )

            if result:
                logger.debug(f"Acquired lock for scheduler {scheduler_id}")
                return True
            else:
                logger.debug(f"Failed to acquire lock for scheduler {scheduler_id}")
                return False

        except Exception as e:
            logger.error(f"Error acquiring lock for scheduler {scheduler_id}: {e}")
            return False

    async def release_scheduler_lock(self, scheduler_id: str) -> bool:
        """
        Release a lock for a specific scheduler.

        Args:
            scheduler_id: ID of the scheduler to unlock

        Returns:
            bool: True if lock was released, False otherwise
        """
        lock_key = f"scheduler_lock:{scheduler_id}"

        try:
            redis_client = await self._get_redis_client()
            result = await redis_client.delete(lock_key)

            if result:
                logger.debug(f"Released lock for scheduler {scheduler_id}")
                return True
            else:
                logger.debug(f"Lock for scheduler {scheduler_id} was not held")
                return False

        except Exception as e:
            logger.error(f"Error releasing lock for scheduler {scheduler_id}: {e}")
            return False

    async def extend_scheduler_lock(
        self, scheduler_id: str, ttl: Optional[int] = None
    ) -> bool:
        """
        Extend the TTL of an existing scheduler lock.

        Args:
            scheduler_id: ID of the scheduler
            ttl: New TTL in seconds

        Returns:
            bool: True if lock was extended, False otherwise
        """
        lock_key = f"scheduler_lock:{scheduler_id}"
        ttl = ttl or self.default_ttl

        try:
            redis_client = await self._get_redis_client()
            result = await redis_client.expire(lock_key, ttl)

            if result:
                logger.debug(f"Extended lock for scheduler {scheduler_id}")
                return True
            else:
                logger.debug(f"Lock for scheduler {scheduler_id} does not exist")
                return False

        except Exception as e:
            logger.error(f"Error extending lock for scheduler {scheduler_id}: {e}")
            return False

    async def is_scheduler_locked(self, scheduler_id: str) -> bool:
        """
        Check if a scheduler is currently locked.

        Args:
            scheduler_id: ID of the scheduler to check

        Returns:
            bool: True if scheduler is locked, False otherwise
        """
        lock_key = f"scheduler_lock:{scheduler_id}"

        try:
            redis_client = await self._get_redis_client()
            result = await redis_client.exists(lock_key)
            return bool(result)

        except Exception as e:
            logger.error(f"Error checking lock for scheduler {scheduler_id}: {e}")
            return False

    async def acquire_global_lock(
        self, lock_name: str, ttl: Optional[int] = None
    ) -> bool:
        """
        Acquire a global lock (not scheduler-specific).

        Args:
            lock_name: Name of the lock
            ttl: Time-to-live for the lock in seconds

        Returns:
            bool: True if lock was acquired, False otherwise
        """
        lock_key = f"global_lock:{lock_name}"
        lock_value = f"{time.time()}"
        ttl = ttl or self.default_ttl

        try:
            redis_client = await self._get_redis_client()
            result = await redis_client.set(lock_key, lock_value, nx=True, ex=ttl)

            if result:
                logger.debug(f"Acquired global lock {lock_name}")
                return True
            else:
                logger.debug(f"Failed to acquire global lock {lock_name}")
                return False

        except Exception as e:
            logger.error(f"Error acquiring global lock {lock_name}: {e}")
            return False

    async def release_global_lock(self, lock_name: str) -> bool:
        """
        Release a global lock.

        Args:
            lock_name: Name of the lock to release

        Returns:
            bool: True if lock was released, False otherwise
        """
        lock_key = f"global_lock:{lock_name}"

        try:
            redis_client = await self._get_redis_client()
            result = await redis_client.delete(lock_key)

            if result:
                logger.debug(f"Released global lock {lock_name}")
                return True
            else:
                logger.debug(f"Global lock {lock_name} was not held")
                return False

        except Exception as e:
            logger.error(f"Error releasing global lock {lock_name}: {e}")
            return False

    async def close(self):
        """Close the Redis connection."""
        if self._redis_client:
            await self._redis_client.close()
            self._redis_client = None
            logger.debug("Closed Redis connection for lock manager")
