"""
Task queue implementation using Redis.

This module provides task queue functionality for asynchronous processing
of scheduler executions and workflow tasks.
"""

import json
import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

import redis.asyncio as redis
from pydantic import BaseModel, Field

from src.utils.config import get_settings
from src.utils.logger import get_logger

logger = get_logger(__name__)


class TaskStatus(str, Enum):
    """Task status enumeration."""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"
    CANCELLED = "cancelled"


class Task(BaseModel):
    """Task model for queue operations."""

    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    queue_name: str = Field(..., description="Name of the queue")
    task_type: str = Field(..., description="Type of task")
    payload: Dict[str, Any] = Field(..., description="Task payload data")
    status: TaskStatus = Field(default=TaskStatus.PENDING)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    max_retries: int = Field(default=3)
    retry_count: int = Field(default=0)
    error_message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class RedisTaskQueue:
    """
    Redis-based task queue implementation.

    Provides asynchronous task queuing and processing capabilities
    using Redis as the backend.
    """

    def __init__(self, redis_url: Optional[str] = None):
        """
        Initialize the task queue.

        Args:
            redis_url: Redis connection URL. If None, uses settings.
        """
        settings = get_settings()
        self.redis_url = redis_url or settings.task_queue_redis_url
        self.default_timeout = settings.task_queue_default_timeout
        self.max_retries = settings.task_queue_max_retries
        self.retry_delay = settings.task_queue_retry_delay
        self._redis_client: Optional[redis.Redis] = None

    async def _get_redis_client(self) -> redis.Redis:
        """Get or create Redis client."""
        if self._redis_client is None:
            self._redis_client = redis.from_url(self.redis_url)
        return self._redis_client

    async def enqueue(self, task: Task) -> bool:
        """
        Enqueue a task for processing.

        Args:
            task: Task to enqueue

        Returns:
            bool: True if task was enqueued successfully
        """
        try:
            redis_client = await self._get_redis_client()

            # Serialize task
            task_data = task.model_dump_json()

            # Add to queue
            queue_key = f"queue:{task.queue_name}"
            await redis_client.lpush(queue_key, task_data)

            # Store task metadata
            task_key = f"task:{task.id}"
            await redis_client.setex(task_key, 3600, task_data)  # 1 hour TTL

            logger.debug(f"Enqueued task {task.id} to queue {task.queue_name}")
            return True

        except Exception as e:
            logger.error(f"Error enqueuing task {task.id}: {e}")
            return False

    async def dequeue(
        self, queue_name: str, timeout: Optional[int] = None
    ) -> Optional[Task]:
        """
        Dequeue a task from the specified queue.

        Args:
            queue_name: Name of the queue to dequeue from
            timeout: Timeout in seconds for blocking operation

        Returns:
            Optional[Task]: Dequeued task or None if timeout
        """
        try:
            redis_client = await self._get_redis_client()
            queue_key = f"queue:{queue_name}"
            timeout = timeout or self.default_timeout

            # Blocking pop from queue
            result = await redis_client.brpop(queue_key, timeout=timeout)

            if result:
                _, task_data = result
                task_dict = json.loads(task_data)
                task = Task(**task_dict)

                # Update task status
                task.status = TaskStatus.RUNNING
                task.started_at = datetime.utcnow()

                # Update task metadata
                await self.update_task_status(task)

                logger.debug(f"Dequeued task {task.id} from queue {queue_name}")
                return task

            return None

        except Exception as e:
            logger.error(f"Error dequeuing from queue {queue_name}: {e}")
            return None

    async def update_task_status(self, task: Task) -> bool:
        """
        Update task status and metadata.

        Args:
            task: Task with updated status

        Returns:
            bool: True if update was successful
        """
        try:
            redis_client = await self._get_redis_client()
            task_key = f"task:{task.id}"

            # Update task data
            task_data = task.model_dump_json()
            await redis_client.setex(task_key, 3600, task_data)

            logger.debug(f"Updated task {task.id} status to {task.status}")
            return True

        except Exception as e:
            logger.error(f"Error updating task {task.id} status: {e}")
            return False

    async def complete_task(
        self, task: Task, result: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Mark a task as completed.

        Args:
            task: Task to complete
            result: Optional result data

        Returns:
            bool: True if task was marked as completed
        """
        task.status = TaskStatus.COMPLETED
        task.completed_at = datetime.utcnow()
        task.result = result

        return await self.update_task_status(task)

    async def fail_task(
        self, task: Task, error_message: str, retry: bool = True
    ) -> bool:
        """
        Mark a task as failed.

        Args:
            task: Task to fail
            error_message: Error message
            retry: Whether to retry the task

        Returns:
            bool: True if task was processed
        """
        task.error_message = error_message

        if retry and task.retry_count < task.max_retries:
            # Retry the task
            task.status = TaskStatus.RETRYING
            task.retry_count += 1

            # Re-enqueue for retry
            await self.enqueue(task)
            logger.info(f"Retrying task {task.id} (attempt {task.retry_count})")
        else:
            # Mark as failed
            task.status = TaskStatus.FAILED
            task.completed_at = datetime.utcnow()
            logger.error(f"Task {task.id} failed permanently: {error_message}")

        return await self.update_task_status(task)

    async def get_task(self, task_id: str) -> Optional[Task]:
        """
        Get task by ID.

        Args:
            task_id: Task ID

        Returns:
            Optional[Task]: Task if found, None otherwise
        """
        try:
            redis_client = await self._get_redis_client()
            task_key = f"task:{task_id}"

            task_data = await redis_client.get(task_key)
            if task_data:
                task_dict = json.loads(task_data)
                return Task(**task_dict)

            return None

        except Exception as e:
            logger.error(f"Error getting task {task_id}: {e}")
            return None

    async def get_queue_length(self, queue_name: str) -> int:
        """
        Get the length of a queue.

        Args:
            queue_name: Name of the queue

        Returns:
            int: Number of tasks in the queue
        """
        try:
            redis_client = await self._get_redis_client()
            queue_key = f"queue:{queue_name}"
            return await redis_client.llen(queue_key)

        except Exception as e:
            logger.error(f"Error getting queue length for {queue_name}: {e}")
            return 0

    async def list_queues(self) -> List[str]:
        """
        List all available queues.

        Returns:
            List[str]: List of queue names
        """
        try:
            redis_client = await self._get_redis_client()
            keys = await redis_client.keys("queue:*")
            return [key.decode().replace("queue:", "") for key in keys]

        except Exception as e:
            logger.error(f"Error listing queues: {e}")
            return []

    async def purge_queue(self, queue_name: str) -> int:
        """
        Purge all tasks from a queue.

        Args:
            queue_name: Name of the queue to purge

        Returns:
            int: Number of tasks removed
        """
        try:
            redis_client = await self._get_redis_client()
            queue_key = f"queue:{queue_name}"
            return await redis_client.delete(queue_key)

        except Exception as e:
            logger.error(f"Error purging queue {queue_name}: {e}")
            return 0

    async def close(self):
        """Close the Redis connection."""
        if self._redis_client:
            await self._redis_client.close()
            self._redis_client = None
            logger.debug("Closed Redis connection for task queue")
