"""
Unit tests for the ScheduleParser class.

Tests the schedule parsing logic for different frequency types.
"""

import pytest
from datetime import datetime, timezone, timedelta
from unittest.mock import patch

from src.utils.schedule_parser import ScheduleParser, InvalidScheduleConfigError
from src.schemas.scheduler import ScheduleFrequency, SimplifiedSchedulerResponse


@pytest.mark.unit
def test_every_minute_schedule():
    """Test every minute schedule parsing."""
    config = {
        "frequency": "every_minute",
        "timezone": "UTC",
    }

    now = datetime.now(timezone.utc)
    next_run = ScheduleParser.get_next_run_time(config)

    # Should be approximately 1 minute from now
    assert next_run > now
    assert next_run <= now + timedelta(minutes=2)


@pytest.mark.unit
def test_hourly_schedule():
    """Test hourly schedule parsing."""
    config = {
        "frequency": "hourly",
        "timezone": "UTC",
    }

    now = datetime.now(timezone.utc)
    next_run = ScheduleParser.get_next_run_time(config)

    # Should be approximately 1 hour from now
    assert next_run > now
    assert next_run <= now + timedelta(hours=2)


@pytest.mark.unit
def test_daily_schedule():
    """Test daily schedule parsing."""
    config = {
        "frequency": "daily",
        "time": "10:30",
        "timezone": "UTC",
    }

    next_run = ScheduleParser.get_next_run_time(config)

    # Should be at 10:30 UTC
    assert next_run.hour == 10
    assert next_run.minute == 30
    assert next_run.second == 0


@pytest.mark.unit
def test_daily_schedule_past_time():
    """Test daily schedule when time has already passed today."""
    # Mock current time to be 15:00
    mock_now = datetime(2025, 1, 17, 15, 0, 0, tzinfo=timezone.utc)

    config = {
        "frequency": "daily",
        "time": "10:30",
        "timezone": "UTC",
    }

    with patch("scheduler_service.utils.schedule_parser.datetime") as mock_datetime:
        mock_datetime.now.return_value = mock_now
        mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)

        next_run = ScheduleParser.get_next_run_time(config)

        # Should be tomorrow at 10:30
        expected_date = mock_now.date() + timedelta(days=1)
        assert next_run.date() == expected_date
        assert next_run.hour == 10
        assert next_run.minute == 30


@pytest.mark.unit
def test_weekly_schedule():
    """Test weekly schedule parsing."""
    config = {
        "frequency": "weekly",
        "time": "14:00",
        "days_of_week": ["Monday", "Wednesday", "Friday"],
        "timezone": "UTC",
    }

    next_run = ScheduleParser.get_next_run_time(config)

    # Should be at 14:00 on one of the specified days
    assert next_run.hour == 14
    assert next_run.minute == 0
    assert next_run.weekday() in [0, 2, 4]  # Monday=0, Wednesday=2, Friday=4


@pytest.mark.unit
def test_monthly_schedule():
    """Test monthly schedule parsing."""
    config = {
        "frequency": "monthly",
        "time": "09:00",
        "days_of_month": [1, 15],
        "timezone": "UTC",
    }

    next_run = ScheduleParser.get_next_run_time(config)

    # Should be at 09:00 on the 1st or 15th
    assert next_run.hour == 9
    assert next_run.minute == 0
    assert next_run.day in [1, 15]


@pytest.mark.unit
def test_custom_schedule():
    """Test custom schedule with cron expression."""
    config = {
        "frequency": "custom",
        "cron_expression": "0 9 * * 1-5",  # 9 AM on weekdays
        "timezone": "UTC",
    }

    next_run = ScheduleParser.get_next_run_time(config)

    # Should be at 9 AM on a weekday
    assert next_run.hour == 9
    assert next_run.minute == 0
    assert next_run.weekday() < 5  # Monday=0 to Friday=4


@pytest.mark.unit
def test_timezone_handling():
    """Test timezone handling in schedule parsing."""
    config = {
        "frequency": "daily",
        "time": "10:30",
        "timezone": "America/New_York",
    }

    next_run = ScheduleParser.get_next_run_time(config)

    # Result should be in UTC
    assert next_run.tzinfo == timezone.utc


@pytest.mark.unit
def test_simplified_scheduler_response():
    """Test parsing with SimplifiedSchedulerResponse object."""
    now = datetime.now(timezone.utc)
    scheduler = SimplifiedSchedulerResponse(
        id="test-123",
        name="Test Scheduler",
        frequency=ScheduleFrequency.DAILY,
        time="10:30",
        timezone="UTC",
        is_active=True,
        workflow_id="workflow-123",
        user_id="user-123",
        created_at=now,
        updated_at=now,
        last_run_at=None,
        next_run_at=None,
    )

    next_run = ScheduleParser.get_next_run_time(scheduler)

    assert next_run.hour == 10
    assert next_run.minute == 30


@pytest.mark.unit
def test_invalid_frequency():
    """Test handling of invalid frequency."""
    config = {
        "frequency": "invalid_frequency",
        "timezone": "UTC",
    }

    with pytest.raises(InvalidScheduleConfigError):
        ScheduleParser.get_next_run_time(config)


@pytest.mark.unit
def test_missing_required_fields():
    """Test handling of missing required fields."""
    # Daily without time
    config = {
        "frequency": "daily",
        "timezone": "UTC",
    }

    with pytest.raises(InvalidScheduleConfigError):
        ScheduleParser.get_next_run_time(config)


@pytest.mark.unit
def test_weekly_without_days():
    """Test weekly schedule without days of week."""
    config = {
        "frequency": "weekly",
        "time": "10:30",
        "timezone": "UTC",
    }

    with pytest.raises(InvalidScheduleConfigError):
        ScheduleParser.get_next_run_time(config)


@pytest.mark.unit
def test_monthly_without_days():
    """Test monthly schedule without days of month."""
    config = {
        "frequency": "monthly",
        "time": "10:30",
        "timezone": "UTC",
    }

    with pytest.raises(InvalidScheduleConfigError):
        ScheduleParser.get_next_run_time(config)


@pytest.mark.unit
def test_custom_without_cron():
    """Test custom schedule without cron expression."""
    config = {
        "frequency": "custom",
        "timezone": "UTC",
    }

    with pytest.raises(InvalidScheduleConfigError):
        ScheduleParser.get_next_run_time(config)


@pytest.mark.unit
def test_invalid_timezone():
    """Test handling of invalid timezone."""
    config = {
        "frequency": "daily",
        "time": "10:30",
        "timezone": "Invalid/Timezone",
    }

    with pytest.raises(InvalidScheduleConfigError):
        ScheduleParser.get_next_run_time(config)


@pytest.mark.unit
def test_invalid_cron_expression():
    """Test handling of invalid cron expression."""
    config = {
        "frequency": "custom",
        "cron_expression": "invalid cron",
        "timezone": "UTC",
    }

    with pytest.raises(InvalidScheduleConfigError):
        ScheduleParser.get_next_run_time(config)


@pytest.mark.unit
def test_with_last_run_time():
    """Test schedule parsing with last run time."""
    last_run = datetime(2025, 1, 17, 10, 30, 0, tzinfo=timezone.utc)

    config = {
        "frequency": "every_minute",
        "timezone": "UTC",
    }

    next_run = ScheduleParser.get_next_run_time(config, last_run)

    # Should be 1 minute after last run
    expected = last_run + timedelta(minutes=1)
    assert next_run >= expected
