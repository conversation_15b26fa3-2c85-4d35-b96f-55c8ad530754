"""
Test configuration and fixtures for the Scheduler Service.

This module provides pytest fixtures and configuration for testing
the scheduler service components.
"""

import asyncio
import os
import pytest
import pytest_asyncio
from datetime import datetime, timezone
from typing import AsyncGenerator, Generator
from unittest.mock import AsyncMock, MagicMock

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import StaticPool

from src.database.connection import Base
from src.core.scheduler_manager import SchedulerManager
from src.core.distributed_lock import LockManager
from src.core.task_queue import RedisTaskQueue
from src.schemas.scheduler import (
    SimplifiedSchedulerCreate,
    SimplifiedSchedulerResponse,
    ScheduleFrequency,
)
from src.utils.config import Settings


# Test database URL (in-memory SQLite for fast tests)
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture
async def test_engine():
    """Create a test database engine."""
    engine = create_async_engine(
        TEST_DATABASE_URL,
        poolclass=StaticPool,
        connect_args={"check_same_thread": False},
        echo=False,
    )

    # Create all tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    yield engine

    # Clean up
    await engine.dispose()


@pytest_asyncio.fixture
async def test_session(test_engine) -> AsyncGenerator[AsyncSession, None]:
    """Create a test database session."""
    async_session = async_sessionmaker(
        test_engine, class_=AsyncSession, expire_on_commit=False
    )

    async with async_session() as session:
        yield session


@pytest.fixture
def test_settings() -> Settings:
    """Create test settings."""
    return Settings(
        debug=True,
        database_url=TEST_DATABASE_URL,
        redis_url="redis://localhost:6379/15",  # Use test database
        log_level="DEBUG",
        log_format="text",
        secret_key="test-secret-key",
        api_key="test-api-key",
        workflow_service_url="http://test-workflow-service",
        workflow_service_api_key="test-workflow-api-key",
    )


@pytest_asyncio.fixture
async def scheduler_manager(test_session) -> SchedulerManager:
    """Create a scheduler manager for testing."""
    return SchedulerManager(test_session)


@pytest.fixture
def mock_lock_manager() -> LockManager:
    """Create a mock lock manager for testing."""
    mock = MagicMock(spec=LockManager)
    mock.acquire_scheduler_lock = AsyncMock(return_value=True)
    mock.release_scheduler_lock = AsyncMock(return_value=True)
    mock.close = AsyncMock()
    return mock


@pytest.fixture
def mock_task_queue() -> RedisTaskQueue:
    """Create a mock task queue for testing."""
    mock = MagicMock(spec=RedisTaskQueue)
    mock.enqueue = AsyncMock(return_value=True)
    mock.close = AsyncMock()
    return mock


@pytest.fixture
def sample_scheduler_create() -> SimplifiedSchedulerCreate:
    """Create a sample scheduler creation request."""
    return SimplifiedSchedulerCreate(
        name="Test Daily Scheduler",
        frequency=ScheduleFrequency.DAILY,
        time="10:30",
        timezone="UTC",
        is_active=True,
        workflow_id="test-workflow-123",
        user_id="test-user-123",
    )


@pytest.fixture
def sample_scheduler_response() -> SimplifiedSchedulerResponse:
    """Create a sample scheduler response."""
    now = datetime.now(timezone.utc)
    return SimplifiedSchedulerResponse(
        id="test-scheduler-123",
        name="Test Daily Scheduler",
        frequency=ScheduleFrequency.DAILY,
        time="10:30",
        timezone="UTC",
        is_active=True,
        workflow_id="test-workflow-123",
        user_id="test-user-123",
        created_at=now,
        updated_at=now,
        last_run_at=None,
        next_run_at=now,
    )


@pytest.fixture
def sample_weekly_scheduler() -> SimplifiedSchedulerCreate:
    """Create a sample weekly scheduler."""
    return SimplifiedSchedulerCreate(
        name="Test Weekly Scheduler",
        frequency=ScheduleFrequency.WEEKLY,
        time="14:00",
        days_of_week=["Monday", "Wednesday", "Friday"],
        timezone="America/New_York",
        is_active=True,
        workflow_id="test-workflow-456",
        user_id="test-user-123",
    )


@pytest.fixture
def sample_monthly_scheduler() -> SimplifiedSchedulerCreate:
    """Create a sample monthly scheduler."""
    return SimplifiedSchedulerCreate(
        name="Test Monthly Scheduler",
        frequency=ScheduleFrequency.MONTHLY,
        time="09:00",
        days_of_month=[1, 15],
        timezone="Europe/London",
        is_active=True,
        workflow_id="test-workflow-789",
        user_id="test-user-123",
    )


@pytest.fixture
def sample_custom_scheduler() -> SimplifiedSchedulerCreate:
    """Create a sample custom scheduler with cron expression."""
    return SimplifiedSchedulerCreate(
        name="Test Custom Scheduler",
        frequency=ScheduleFrequency.CUSTOM,
        cron_expression="0 9 * * 1-5",  # 9 AM on weekdays
        timezone="Asia/Tokyo",
        is_active=True,
        workflow_id="test-workflow-custom",
        user_id="test-user-123",
    )


@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch):
    """Set up test environment variables."""
    monkeypatch.setenv("DATABASE_URL", TEST_DATABASE_URL)
    monkeypatch.setenv("REDIS_URL", "redis://localhost:6379/15")
    monkeypatch.setenv("LOG_LEVEL", "DEBUG")
    monkeypatch.setenv("DEBUG", "true")


@pytest.fixture
def mock_workflow_executor():
    """Create a mock workflow executor."""
    mock = MagicMock()
    mock.execute_workflow = AsyncMock(
        return_value={"status": "success", "result": "test"}
    )
    mock.validate_workflow = AsyncMock(return_value=True)
    mock.health_check = AsyncMock(return_value=True)
    return mock


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line("markers", "unit: Unit tests")
    config.addinivalue_line("markers", "integration: Integration tests")
    config.addinivalue_line("markers", "e2e: End-to-end tests")
    config.addinivalue_line("markers", "slow: Slow running tests")


# Skip tests that require Redis if not available
def pytest_collection_modifyitems(config, items):
    """Modify test collection to skip Redis tests if Redis is not available."""
    try:
        import redis

        redis_client = redis.Redis(host="localhost", port=6379, db=15)
        redis_client.ping()
        redis_available = True
    except Exception:
        redis_available = False

    if not redis_available:
        skip_redis = pytest.mark.skip(reason="Redis not available")
        for item in items:
            if "redis" in item.keywords or "integration" in item.keywords:
                item.add_marker(skip_redis)
