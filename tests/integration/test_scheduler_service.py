"""
Integration tests for the Scheduler Service.

Tests the integration between different components of the scheduler service.
"""

import pytest
import asyncio
from datetime import datetime, timezone, timedelta
from unittest.mock import AsyncMock, patch

from src.core.scheduler_service import SchedulerService
from src.core.scheduler_manager import SchedulerManager
from src.schemas.scheduler import SimplifiedSchedulerCreate, ScheduleFrequency


@pytest.mark.integration
@pytest.mark.asyncio
async def test_scheduler_service_initialization():
    """Test scheduler service initialization."""
    service = SchedulerService(
        redis_url="redis://localhost:6379/15",
        max_concurrent_schedulers=5,
        batch_size=10,
        worker_concurrency=2,
    )

    # Mock Redis and other external dependencies
    with (
        patch("scheduler_service.core.distributed_lock.LockManager") as mock_lock,
        patch("scheduler_service.core.task_queue.RedisTaskQueue") as mock_queue,
        patch(
            "scheduler_service.core.instance_coordinator.InstanceCoordinator"
        ) as mock_coordinator,
    ):

        mock_lock.return_value.close = AsyncMock()
        mock_queue.return_value.close = AsyncMock()
        mock_coordinator.return_value.start = AsyncMock()
        mock_coordinator.return_value.stop = AsyncMock()
        mock_coordinator.return_value.is_leader = AsyncMock(return_value=True)

        await service.initialize()

        assert service.lock_manager is not None
        assert service.task_queue is not None
        assert service.instance_coordinator is not None


@pytest.mark.integration
@pytest.mark.asyncio
async def test_scheduler_service_lifecycle():
    """Test scheduler service start and stop lifecycle."""
    service = SchedulerService(
        redis_url="redis://localhost:6379/15",
        max_concurrent_schedulers=5,
        batch_size=10,
        worker_concurrency=2,
    )

    # Mock external dependencies
    with (
        patch("scheduler_service.core.distributed_lock.LockManager") as mock_lock,
        patch("scheduler_service.core.task_queue.RedisTaskQueue") as mock_queue,
        patch(
            "scheduler_service.core.instance_coordinator.InstanceCoordinator"
        ) as mock_coordinator,
    ):

        mock_lock.return_value.close = AsyncMock()
        mock_queue.return_value.close = AsyncMock()
        mock_coordinator.return_value.start = AsyncMock()
        mock_coordinator.return_value.stop = AsyncMock()
        mock_coordinator.return_value.is_leader = AsyncMock(return_value=True)

        await service.initialize()

        # Start service
        await service.start()
        assert service.is_running() is True

        # Stop service
        await service.stop()
        assert service.is_running() is False


@pytest.mark.integration
@pytest.mark.asyncio
async def test_end_to_end_scheduler_creation_and_execution(test_session):
    """Test end-to-end scheduler creation and execution flow."""
    # Create scheduler manager
    scheduler_manager = SchedulerManager(test_session)

    # Create a scheduler that should be due immediately
    scheduler_data = SimplifiedSchedulerCreate(
        name="Test Integration Scheduler",
        frequency=ScheduleFrequency.EVERY_MINUTE,
        timezone="UTC",
        is_active=True,
        workflow_id="test-workflow-integration",
        user_id="test-user-integration",
    )

    # Create scheduler
    scheduler = await scheduler_manager.create(scheduler_data, "test-user-integration")
    assert scheduler is not None

    # Verify scheduler was created with correct properties
    assert scheduler.name == "Test Integration Scheduler"
    assert scheduler.frequency == ScheduleFrequency.EVERY_MINUTE
    assert scheduler.is_active is True
    assert scheduler.next_run_at is not None

    # Simulate making the scheduler due by setting next_run_at to the past
    past_time = datetime.now(timezone.utc) - timedelta(minutes=1)

    # Update the scheduler to be due
    from src.database.models import Scheduler
    from sqlalchemy import select, update

    stmt = (
        update(Scheduler)
        .where(Scheduler.id == scheduler.id)
        .values(next_run_at=past_time)
    )
    await test_session.execute(stmt)
    await test_session.commit()

    # Get due schedulers
    due_schedulers = await scheduler_manager.get_due_schedulers(limit=10)

    # Verify our scheduler is in the due list
    due_ids = [s.id for s in due_schedulers]
    assert scheduler.id in due_ids

    # Verify the due scheduler has the correct properties
    due_scheduler = next(s for s in due_schedulers if s.id == scheduler.id)
    assert due_scheduler.name == "Test Integration Scheduler"
    assert due_scheduler.is_active is True


@pytest.mark.integration
@pytest.mark.asyncio
async def test_scheduler_engine_processing(
    test_session, mock_task_queue, mock_lock_manager
):
    """Test scheduler engine processing of due schedulers."""
    from src.core.scheduler_engine import SchedulerEngine

    # Create scheduler manager and add a due scheduler
    scheduler_manager = SchedulerManager(test_session)

    scheduler_data = SimplifiedSchedulerCreate(
        name="Test Engine Processing",
        frequency=ScheduleFrequency.DAILY,
        time="10:00",
        timezone="UTC",
        is_active=True,
        workflow_id="test-workflow-engine",
        user_id="test-user-engine",
    )

    # Create scheduler
    scheduler = await scheduler_manager.create(scheduler_data, "test-user-engine")

    # Make scheduler due by setting next_run_at to the past
    from src.database.models import Scheduler
    from sqlalchemy import update

    past_time = datetime.now(timezone.utc) - timedelta(minutes=1)
    stmt = (
        update(Scheduler)
        .where(Scheduler.id == scheduler.id)
        .values(next_run_at=past_time)
    )
    await test_session.execute(stmt)
    await test_session.commit()

    # Create scheduler engine
    engine = SchedulerEngine(
        db_session=test_session,
        task_queue=mock_task_queue,
        lock_manager=mock_lock_manager,
        batch_size=10,
        max_concurrency=5,
    )

    # Process due schedulers
    await engine.process_due_schedulers()

    # Verify task was enqueued
    mock_task_queue.enqueue.assert_called_once()

    # Verify lock was acquired and released
    mock_lock_manager.acquire_scheduler_lock.assert_called_once()
    mock_lock_manager.release_scheduler_lock.assert_called_once()


@pytest.mark.integration
@pytest.mark.asyncio
async def test_multiple_schedulers_processing(
    test_session, mock_task_queue, mock_lock_manager
):
    """Test processing multiple due schedulers."""
    from src.core.scheduler_engine import SchedulerEngine

    scheduler_manager = SchedulerManager(test_session)

    # Create multiple schedulers
    schedulers = []
    for i in range(3):
        scheduler_data = SimplifiedSchedulerCreate(
            name=f"Test Scheduler {i}",
            frequency=ScheduleFrequency.HOURLY,
            timezone="UTC",
            is_active=True,
            workflow_id=f"test-workflow-{i}",
            user_id="test-user-multi",
        )
        scheduler = await scheduler_manager.create(scheduler_data, "test-user-multi")
        schedulers.append(scheduler)

    # Make all schedulers due
    from src.database.models import Scheduler
    from sqlalchemy import update

    past_time = datetime.now(timezone.utc) - timedelta(minutes=1)
    for scheduler in schedulers:
        stmt = (
            update(Scheduler)
            .where(Scheduler.id == scheduler.id)
            .values(next_run_at=past_time)
        )
        await test_session.execute(stmt)
    await test_session.commit()

    # Create scheduler engine
    engine = SchedulerEngine(
        db_session=test_session,
        task_queue=mock_task_queue,
        lock_manager=mock_lock_manager,
        batch_size=10,
        max_concurrency=5,
    )

    # Process due schedulers
    await engine.process_due_schedulers()

    # Verify all tasks were enqueued
    assert mock_task_queue.enqueue.call_count == 3

    # Verify locks were acquired for all schedulers
    assert mock_lock_manager.acquire_scheduler_lock.call_count == 3
    assert mock_lock_manager.release_scheduler_lock.call_count == 3


@pytest.mark.integration
@pytest.mark.asyncio
async def test_scheduler_health_check():
    """Test scheduler service health check."""
    service = SchedulerService(
        redis_url="redis://localhost:6379/15",
        max_concurrent_schedulers=5,
        batch_size=10,
        worker_concurrency=2,
    )

    # Mock external dependencies for health check
    with (
        patch(
            "scheduler_service.database.connection.DatabaseManager.test_connection"
        ) as mock_db_health,
        patch("redis.asyncio.Redis.from_url") as mock_redis,
    ):

        mock_db_health.return_value = True
        mock_redis_instance = AsyncMock()
        mock_redis_instance.ping = AsyncMock()
        mock_redis_instance.close = AsyncMock()
        mock_redis.return_value = mock_redis_instance

        # Test health check
        is_healthy = await service.health_check()
        assert is_healthy is True


@pytest.mark.integration
@pytest.mark.asyncio
async def test_scheduler_status():
    """Test getting scheduler service status."""
    service = SchedulerService(
        redis_url="redis://localhost:6379/15",
        max_concurrent_schedulers=5,
        batch_size=10,
        worker_concurrency=2,
    )

    # Mock external dependencies
    with patch(
        "scheduler_service.database.connection.DatabaseManager.test_connection"
    ) as mock_db_health:
        mock_db_health.return_value = True

        status = await service.get_status()

        assert status["service"] == "scheduler-service"
        assert "is_running" in status
        assert "components" in status
        assert "metrics" in status
