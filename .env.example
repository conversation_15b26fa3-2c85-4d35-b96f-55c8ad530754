# Scheduler Service Environment Configuration
# Copy this file to .env and update the values for your environment

# Application Configuration
DEBUG=false
HOST=0.0.0.0
PORT=8000
LOG_LEVEL=INFO
LOG_FORMAT=json

# Database Configuration
DATABASE_URL=postgresql+asyncpg://scheduler_user:scheduler_password@localhost:5432/scheduler_db

# Database Pool Configuration
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=10
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600
DB_ECHO=false

# Workflow Service Configuration
WORKFLOW_SERVICE_URL=http://localhost:8001
WORKFLOW_SERVICE_API_KEY=your-workflow-service-api-key
WORKFLOW_SERVICE_TIMEOUT=60

# Auth Service Configuration
AUTH_SERVICE_URL=http://localhost:8002
AUTH_SERVICE_TIMEOUT=30

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Task Queue Configuration
TASK_QUEUE_REDIS_URL=redis://localhost:6379/0
TASK_QUEUE_DEFAULT_TIMEOUT=30
TASK_QUEUE_MAX_RETRIES=3
TASK_QUEUE_RETRY_DELAY=5

# Distributed Locking Configuration
DISTRIBUTED_LOCK_REDIS_URL=redis://localhost:6379/0
DISTRIBUTED_LOCK_DEFAULT_TTL=300
DISTRIBUTED_LOCK_RETRY_DELAY=0.1
DISTRIBUTED_LOCK_MAX_RETRIES=10

# Instance Configuration
HOSTNAME=localhost
INSTANCE_ID=scheduler-instance-1
VERSION=0.1.0

# Security
SECRET_KEY=your-secret-key-change-in-production
API_KEY=your-api-key-change-in-production

# HTTP Configuration
HTTP_TIMEOUT=30

# Retry Configuration
MAX_RETRY_ATTEMPTS=5
RETRY_BACKOFF_FACTOR=2.0
RETRY_MAX_DELAY=300

# Scheduler Configuration
SCHEDULER_BATCH_SIZE=50
SCHEDULER_CONCURRENCY=10
SCHEDULER_CYCLE_INTERVAL=30

# Production Scalability Configuration
ENABLE_CONCURRENT_PROCESSING=true
MAX_CONCURRENT_SCHEDULERS=100
ENABLE_TASK_QUEUE=true
ENABLE_DISTRIBUTED_LOCKING=true

# Task Worker Configuration
TASK_WORKER_CONCURRENCY=5
TASK_WORKER_QUEUES=workflow_execution,scheduler_tasks
TASK_WORKER_TIMEOUT=300

# Monitoring and Metrics
ENABLE_METRICS=true
METRICS_PORT=9090
ENABLE_SCHEDULER_METRICS=true
ENABLE_TASK_QUEUE_METRICS=true

# Health Check Configuration
HEALTH_CHECK_REDIS=true
HEALTH_CHECK_DATABASE=true
HEALTH_CHECK_TIMEOUT=10

# Production Mode Settings
PRODUCTION_MODE=false
