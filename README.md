# Scheduler Service

A standalone, production-ready scheduler service for workflow automation. This service provides reliable, scalable scheduling capabilities with support for various schedule types including daily, weekly, monthly, and custom cron expressions.

## Features

- **Multiple Schedule Types**: Support for every minute, hourly, daily, weekly, monthly, and custom cron schedules
- **Timezone Support**: Full timezone support with automatic DST handling
- **Distributed Processing**: Redis-based distributed locking for multi-instance deployments
- **Task Queue Integration**: Asynchronous task processing with Redis-based queuing
- **High Availability**: Instance coordination and leader election for fault tolerance
- **Comprehensive Monitoring**: Built-in metrics, health checks, and logging
- **RESTful API**: Complete REST API for scheduler management
- **Database Migrations**: Alembic-based database schema management
- **Docker Support**: Production-ready Docker containers and compose files

## Quick Start

### Using Docker Compose (Recommended)

1. Clone the repository and navigate to the scheduler service:
```bash
git clone <repository-url>
cd scheduler-service
```

2. Copy the environment file and configure:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. Start the services:
```bash
docker-compose up -d
```

4. Check service health:
```bash
curl http://localhost:8000/health
```

### Local Development

1. Install dependencies:
```bash
make install
```

2. Set up the database:
```bash
make db-init
make db-migrate
```

3. Run the development server:
```bash
make dev
```

## Architecture

The Scheduler Service consists of several key components:

- **Scheduler Engine**: Core processing engine that identifies and executes due schedulers
- **Scheduler Manager**: CRUD operations and lifecycle management for schedulers
- **Task Queue**: Redis-based asynchronous task processing
- **Distributed Lock Manager**: Prevents duplicate processing across instances
- **Instance Coordinator**: Manages multiple service instances and leader election
- **Workflow Executor**: Interfaces with external workflow services
- **REST API**: HTTP endpoints for scheduler management

## API Documentation

### Create a Scheduler

```bash
POST /api/v1/schedulers?user_id=user123
Content-Type: application/json

{
  "name": "Daily Report",
  "frequency": "daily",
  "time": "09:00",
  "timezone": "America/New_York",
  "is_active": true,
  "workflow_id": "report-workflow-123"
}
```

### List Schedulers

```bash
GET /api/v1/schedulers?user_id=user123&limit=50&skip=0
```

### Update a Scheduler

```bash
PUT /api/v1/schedulers/{scheduler_id}?user_id=user123
Content-Type: application/json

{
  "name": "Updated Daily Report",
  "time": "10:00",
  "is_active": false
}
```

### Delete a Scheduler

```bash
DELETE /api/v1/schedulers/{scheduler_id}?user_id=user123
```

## Schedule Types

### Daily Schedule
```json
{
  "frequency": "daily",
  "time": "14:30",
  "timezone": "UTC"
}
```

### Weekly Schedule
```json
{
  "frequency": "weekly",
  "time": "09:00",
  "days_of_week": ["Monday", "Wednesday", "Friday"],
  "timezone": "America/New_York"
}
```

### Monthly Schedule
```json
{
  "frequency": "monthly",
  "time": "08:00",
  "days_of_month": [1, 15],
  "timezone": "Europe/London"
}
```

### Custom Cron Schedule
```json
{
  "frequency": "custom",
  "cron_expression": "0 9 * * 1-5",
  "timezone": "Asia/Tokyo"
}
```

## Configuration

Key environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection URL | Required |
| `REDIS_URL` | Redis connection URL | `redis://localhost:6379/0` |
| `WORKFLOW_SERVICE_URL` | External workflow service URL | Required |
| `SCHEDULER_BATCH_SIZE` | Batch size for processing | `50` |
| `SCHEDULER_CONCURRENCY` | Max concurrent processing | `10` |
| `ENABLE_DISTRIBUTED_LOCKING` | Enable distributed locks | `true` |
| `ENABLE_TASK_QUEUE` | Enable async task queue | `true` |

See `.env.example` for complete configuration options.

## Deployment

### Production Deployment

1. Build the production image:
```bash
docker build -t scheduler-service:latest .
```

2. Deploy with Docker Compose:
```bash
docker-compose -f docker-compose.yml up -d
```

### Kubernetes Deployment

Kubernetes manifests are available in the `k8s/` directory:

```bash
kubectl apply -f k8s/
```

### Scaling

The service supports horizontal scaling:

1. **Multiple Instances**: Deploy multiple instances with shared Redis and PostgreSQL
2. **Leader Election**: Automatic leader election ensures only one instance processes schedulers
3. **Load Balancing**: API endpoints can be load balanced across instances

## Monitoring

### Health Checks

- **Service Health**: `GET /health`
- **Database Health**: Included in health check
- **Redis Health**: Included in health check

### Metrics

Prometheus metrics are available at `/metrics` (if enabled):

- Scheduler execution counts
- Processing times
- Error rates
- Queue lengths

### Logging

Structured JSON logging with correlation IDs:

```bash
# View logs
docker-compose logs -f scheduler-service

# Local development
tail -f logs/scheduler.log
```

## Development

### Setup Development Environment

```bash
make setup-dev
```

### Running Tests

```bash
# All tests
make test

# Unit tests only
make test-unit

# Integration tests
make test-integration

# With coverage
make test
```

### Code Quality

```bash
# Format code
make format

# Lint code
make lint

# Type checking
make lint
```

### Database Migrations

```bash
# Create migration
make db-revision MESSAGE="Add new field"

# Apply migrations
make db-migrate

# Rollback migration
make db-downgrade
```

## CLI Usage

The service includes a comprehensive CLI:

```bash
# Run scheduler engine
python -m scheduler_service.cli run-scheduler

# List schedulers
python -m scheduler_service.cli list-schedulers --user-id user123

# Show due schedulers
python -m scheduler_service.cli show-due-schedulers

# Test connections
python -m scheduler_service.cli test-connection
```

## Troubleshooting

### Common Issues

1. **Schedulers not executing**: Check Redis connectivity and distributed locking
2. **Database connection errors**: Verify PostgreSQL configuration and migrations
3. **Timezone issues**: Ensure timezone strings are valid Olson timezone names

### Debug Mode

Enable debug logging:

```bash
export LOG_LEVEL=DEBUG
export LOG_FORMAT=text
```

### Performance Tuning

- Adjust `SCHEDULER_BATCH_SIZE` for processing efficiency
- Tune `SCHEDULER_CONCURRENCY` based on system resources
- Monitor Redis memory usage for task queue
- Use connection pooling for database connections

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Run quality checks: `make ci-test`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:

- Create an issue in the repository
- Check the documentation in the `docs/` directory
- Review the troubleshooting guide

## Changelog

See [CHANGELOG.md](CHANGELOG.md) for version history and changes.
